import { markRaw } from 'vue';

// 工具函数：防止Three.js对象被Vue响应式系统包装
export function createNonReactiveThreeObject(ThreeClass, ...args) {
    return markRaw(new ThreeClass(...args));
}

// 工具函数：为Three.js对象添加非响应式标记
export function makeThreeObjectNonReactive(object) {
    return markRaw(object);
}

// 工具函数：安全地访问Three.js对象属性
export function safeThreeAccess(object, callback) {
    try {
        return callback(object);
    } catch (error) {
        console.warn('Three.js object access error:', error);
        return null;
    }
}
