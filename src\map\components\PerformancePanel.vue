<template>
  <div v-if="visible" class="performance-panel">
    <h3>性能监控</h3>

    <!-- 如果没有渲染器，显示提示 -->
    <div v-if="!renderer" class="performance-warning">
      <p>⚠️ 3D渲染器未初始化</p>
      <p>请等待场景加载完成</p>
    </div>

    <!-- 性能统计数据 -->
    <div v-else class="performance-stats">
      <div class="stat-item">
        <label>FPS:</label>
        <span :class="{ 'low-fps': performanceStats.fps < 30 }">{{ performanceStats.fps }}</span>
      </div>
      <!-- <div class="stat-item">
        <label>内存:</label>
        <span :class="{ 'high-memory': performanceStats.memoryMB > 300 }">{{ performanceStats.memoryMB }}MB</span>
      </div> -->
      <div class="stat-item">
        <label>点数:</label>
        <span :class="{ 'high-points': performanceStats.points > 150000 }">{{ performanceStats.points.toLocaleString()
        }}</span>
      </div>
      <div v-if="pcdRenderer && pcdRenderer.hasPCD()" class="stat-item">
        <label>LOD:</label>
        <span>{{ pcdRenderer.currentLOD || 0 }}</span>
      </div>
      <div v-if="currentMapId" class="stat-item">
        <label>地图:</label>
        <span>{{ currentMapData ? currentMapData.name : '未知' }}</span>
      </div>
      <div v-if="!currentMapId" class="stat-item">
        <label>状态:</label>
        <span style="color: #999;">未加载地图</span>
      </div>
    </div>

    <div class="quality-presets">
      <h4>渲染质量</h4>
      <div class="preset-buttons">
        <a-button size="small" @click="setRenderQuality('performance')">性能</a-button>
        <a-button size="small" @click="setRenderQuality('balanced')">平衡</a-button>
        <a-button size="small" @click="setRenderQuality('quality')">质量</a-button>
      </div>
    </div>

    <div class="performance-actions">
      <a-button size="small" type="primary" @click="optimizePerformance">自动优化</a-button>
      <a-button size="small" @click="handleClose">关闭</a-button>
    </div>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { renderOptimizer } from '../performance/renderOptimizer.js';
import { performanceMonitor } from '../performance/performanceMonitor.js';

export default {
    name: 'PerformancePanel',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        renderer: {
            type: Object,
            default: null,
        },
        pcdRenderer: {
            type: Object,
            default: null,
        },
        pgmRenderer: {
            type: Object,
            default: null,
        },
        currentMapId: {
            type: [String, Number],
            default: null,
        },
        currentMapData: {
            type: Object,
            default: () => ({}),
        },
        performanceStats: {
            type: Object,
            default: () => ({
                fps: 0,
                memoryMB: 0,
                points: 0,
            }),
        },
    },
    emits: ['update:visible', 'optimize-performance'],
    methods: {
    // 关闭面板
        handleClose() {
            this.$emit('update:visible', false);
        },

        // 设置渲染质量预设
        setRenderQuality(preset) {
            renderOptimizer.applyPreset(preset, this.pgmRenderer, this.pcdRenderer);
            message.success(`已应用${preset}渲染预设`);
        },

        // 优化渲染性能
        optimizePerformance() {
            const stats = performanceMonitor.getStats();
            const advice = renderOptimizer.getOptimizationAdvice(stats);

            console.log('性能统计:', stats);
            console.log('优化建议:', advice);

            // 通知父组件执行优化，让父组件处理所有的渲染器修改
            this.$emit('optimize-performance', advice);
        },
    },
};
</script>

<style scoped>
/* 性能监控面板样式 */
.performance-panel {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.performance-panel h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.performance-stats {
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid #e9ecef;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.stat-item span {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.stat-item .low-fps {
  color: #dc3545;
  font-weight: 700;
}

.stat-item .high-memory {
  color: #ff8c00;
  font-weight: 700;
}

.stat-item .high-points {
  color: #ffa500;
  font-weight: 700;
}

.quality-presets {
  margin: 12px 0;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
}

.quality-presets h4 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 13px;
  font-weight: 500;
}

.preset-buttons {
  display: flex;
  gap: 4px;
}

.preset-buttons .ant-btn {
  flex: 1;
  font-size: 12px;
  height: 28px;
}

.performance-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
}

.performance-actions .ant-btn {
  flex: 1;
}

/* 性能监控警告和信息样式 */
.performance-warning {
  text-align: center;
  padding: 16px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  margin-bottom: 12px;
}

.performance-warning p {
  margin: 4px 0;
  color: #856404;
  font-size: 14px;
}

.performance-info {
  text-align: center;
  padding: 16px;
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  border-radius: 4px;
  margin-bottom: 12px;
}

.performance-info p {
  margin: 4px 0;
  color: #0c5460;
  font-size: 14px;
}
</style>
