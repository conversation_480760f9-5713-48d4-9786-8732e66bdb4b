import axios from 'axios';
import { notification } from 'ant-design-vue';
// 创建 axios 实例
const request = axios.create({
    // API 请求的默认前缀
    baseURL: '/',
    timeout: 60000, // 请求超时时间
});

// 异常拦截处理器
const errorHandler = error => {
    console.log(error);
    if (error.response) {
        const data = error.response.data;
        // 从 localstorage 获取 token
        const token = localStorage.getItem('token');
        if (error.response.status === 403) {
            notification.error({
                message: 'Forbidden',
                description: data.message,
            });
        }
        if (error.response.status === 401 && !(data.result && data.result.isLogin)) {
            notification.error({
                message: 'Unauthorized',
                description: 'Authorization verification failed',
            });
            if (token) {
                // store.dispatch('Logout').then(() => {
                //   setTimeout(() => {
                //     window.location.reload();
                //   }, 1500);
                // });
            }
        }
    }
    return Promise.reject(error);
};

// request interceptor
request.interceptors.request.use(config => {
    const token = localStorage.getItem('token');
    // 如果 token 存在
    // 让每个请求携带自定义 token 请根据实际情况自行修改
    if (token) {
        config.headers['token'] = token;
    }
    return config;
}, errorHandler);


// 异常拦截处理器
const responseErrorHandler = error => {
    console.log(error);

    if (error.response.status === 401) {
        notification.error({
            message: error.response.data.status,
            description: error.response.data.message,
        });
    // store.dispatch('Logout').then(() => {
    //   setTimeout(() => {
    //     window.location.reload()
    //   }, 1500)
    // })
    }
    return Promise.reject(error);
};


// response interceptor
request.interceptors.response.use(response => {
    return response.data;
}, responseErrorHandler);


export default request;

