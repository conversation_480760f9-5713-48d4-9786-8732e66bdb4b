/**
 * PGM文件渲染工具模块
 * 处理PGM文件的加载、解析和渲染
 */

/**
 * PGM 特性
 * 1. PGM文件由两部分组成：文件头部分和数据部分
 * 2. 文件头包括的信息依次是：
 * 2.1. PGM文件的格式类型（P2或P5)
 * 2.2. 图像的宽度
 * 2.3. 图像的高度
 * 2.4. 图像灰度值可能的最大值
 * 这些信息在文件头中以ASCII码形式存储，并用分隔符（如空格、TAB、回车符、换行符）分开。
 */

import * as THREE from 'three';
import { markRaw } from 'vue';
import { message } from 'ant-design-vue';
import { YAMLParser } from './yamlParser.js';

/**
 * PGM渲染器类
 */
export class PGMRenderer {
    constructor(scene) {
        this.scene = scene;
        this.pgmMesh = null;
        this.pgmData = null; // 存储PGM数据信息
        this.pgmBounds = null; // 存储PGM边界信息
        this.mapConfig = null; // 存储地图配置信息

        // 性能优化相关
        this.textureCache = new Map(); // 纹理缓存
        this.isRendering = false; // 渲染状态标志
        this.renderQueue = []; // 渲染队列
    }

    /**
     * 渲染PGM文件（支持YAML配置）- 性能优化版本
     */
    async renderPGM(filePath = null, yamlPath = null) {
        if (!filePath) {
            message.warning('未找到此地图的PGM文件，无法渲染地图。');
            return;
        }
        if (!yamlPath) {
            message.warning('未找到此地图的 YAML 文件，渲染地图可能不准确。');
        }
        // 防止重复渲染
        if (this.isRendering) {
            console.warn('PGM正在渲染中，跳过重复请求');
            return;
        }

        this.isRendering = true;

        try {
            // 检查缓存
            const cacheKey = `${filePath}_${yamlPath || ''}`;
            if (this.textureCache.has(cacheKey)) {
                console.log('使用缓存的PGM纹理');
                return this.renderFromCache(cacheKey);
            }

            // 移除之前的PGM网格
            this.removePreviousPGM();

            // 加载YAML配置文件
            await this.loadMapConfig(filePath, yamlPath);

            // 加载PGM文件
            const pgmData = await this.loadPGMFile(filePath);
            this.pgmData = pgmData;

            // 使用YAML配置创建PGM网格（优化版本）
            this.pgmMesh = this.createOptimizedPGMMesh(pgmData, cacheKey);

            // 使用YAML配置计算PGM边界信息
            this.calculatePGMBoundsWithConfig(pgmData);

            // 添加到场景
            this.scene.add(this.pgmMesh);

            // console.log('PGM文件渲染完成:', pgmData)
            // console.log('地图配置:', this.mapConfig)
            // console.log('PGM边界信息:', this.pgmBounds)
            // console.log('=== 新坐标系验证 ===')
            // console.log(`YAML原点: [${this.mapConfig.origin[0]}, ${this.mapConfig.origin[1]}, ${this.mapConfig.origin[2]}]`)
            // console.log(`计算的边界: minY=${this.pgmBounds.minY}, maxY=${this.pgmBounds.maxY}, minZ=${this.pgmBounds.minZ}, maxZ=${this.pgmBounds.maxZ}`)


            return {
                success: true,
                data: pgmData,
                config: this.mapConfig,
                bounds: this.pgmBounds,
                mesh: this.pgmMesh,
            };
        } catch (error) {
            console.error('PGM渲染错误:', error);
            throw error;
        } finally {
            this.isRendering = false;
        }
    }

    /**
     * 加载地图配置文件
     */
    async loadMapConfig(pgmFilePath, yamlPath = null) {
        try {
            // 如果没有指定YAML路径，尝试自动推断
            if (!yamlPath) {
                yamlPath = pgmFilePath.replace('.pgm', '.yaml');
            }

            console.log('尝试加载YAML配置文件:', yamlPath);

            // 加载YAML配置
            this.mapConfig = await YAMLParser.loadYAML(yamlPath);

            // 验证配置
            YAMLParser.validateMapConfig(this.mapConfig);

            console.log('YAML配置加载成功:', this.mapConfig);

        } catch (error) {
            console.warn('YAML配置文件加载失败，使用默认配置:', error.message);

            // 使用默认配置
            this.mapConfig = YAMLParser.getDefaultMapConfig();

            // 根据PGM文件路径设置image字段
            const fileName = pgmFilePath.split('/').pop();
            this.mapConfig.image = fileName;
        }
    }

    /**
     * 缓存渲染PGM
     */
    renderFromCache(cacheKey) {
        const cachedData = this.textureCache.get(cacheKey);
        if (!cachedData) return null;

        // 移除之前的网格
        this.removePreviousPGM();

        // 创建新的网格使用缓存的纹理
        const { texture, bounds, config } = cachedData;
        this.mapConfig = config;
        this.pgmBounds = bounds;

        // 重新创建几何体（几何体不能复用，但纹理可以）
        const { width, height } = this.pgmData || { width: 100, height: 100 };
        const realWidth = width * config.resolution;
        const realHeight = height * config.resolution;

        const geometry = markRaw(new THREE.PlaneGeometry(realWidth, realHeight, Math.min(width - 1, 512), Math.min(height - 1, 512)));
        const material = markRaw(new THREE.MeshBasicMaterial({
            map: texture,
            transparent: true,
            side: THREE.DoubleSide,
        }));

        this.pgmMesh = new THREE.Mesh(geometry, material);
        //Z轴向上，Y轴水平，X轴保持不变
        // PGM地图在XY平面上，不需要旋转
        // this.pgmMesh.rotation.x = -Math.PI / 2  // 移除旋转

        const originX = config.origin[0];
        const originY = config.origin[1];
        const centerX = originX + realWidth / 2;
        // YAML的Y坐标对应新的Y轴坐标
        const centerY = originY + realHeight / 2;
        this.pgmMesh.position.set(centerX, centerY, 0.01);  // Z=0.01稍微抬高避免Z-fighting

        this.pgmMesh.name = 'pgm-map';
        this.pgmMesh.userData = {
            type: 'pgm',
            width: realWidth,
            height: realHeight,
            origin: config.origin,
            resolution: config.resolution,
        };

        this.scene.add(markRaw(this.pgmMesh));

        return {
            success: true,
            data: this.pgmData,
            config: this.mapConfig,
            bounds: this.pgmBounds,
            mesh: this.pgmMesh,
        };
    }

    /**
     * 移除之前的PGM网格 - 优化版本
     */
    removePreviousPGM() {
        if (this.pgmMesh) {
            this.scene.remove(this.pgmMesh);
            // 清理资源，但保留缓存的纹理
            if (this.pgmMesh.geometry) {
                this.pgmMesh.geometry.dispose();
            }
            if (this.pgmMesh.material) {
                // 不清理纹理，因为可能被缓存
                this.pgmMesh.material.dispose();
            }
            this.pgmMesh = null;
        }
    }

    /**
     * 加载PGM文件
     */
    async loadPGMFile(filePath) {
        const response = await fetch(filePath);
        if (!response.ok) {
            throw new Error(`无法加载PGM文件: ${response.status}`);
        }

        const arrayBuffer = await response.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);

        return this.parsePGM(uint8Array);
    }

    /**
     * 解析PGM文件
     */
    parsePGM(data) {
        let offset = 0;

        // 读取头部信息
        let header = '';
        while (offset < data.length) {
            const char = String.fromCharCode(data[offset]); // 将 Unicode 码点转换为对应的字符
            header += char;
            offset++;

            // 检查是否读取完头部（连续两个换行符或足够的信息）
            if (header.includes('\n') && header.split('\n').length >= 4) {
                break;
            }
        }
        console.log('header', header);
        // 将header字符串按行分割，并移除注释行（以#开头）和空行，返回有效内容行数组。
        const lines = header.trim().split('\n').filter(line => !line.startsWith('#'));

        if (lines[0] !== 'P5') {
            throw new Error('不支持的PGM格式，仅支持P5格式');
        }
        console.log('PGM格式:', lines);

        const [width, height] = lines[1].split(' ').map(Number);
        const maxVal = parseInt(lines[2]);

        // 读取像素数据
        const pixelData = data.slice(offset);

        if (pixelData.length < width * height) {
            throw new Error('PGM文件数据不完整');
        }

        return {
            width,
            height,
            maxVal,
            data: pixelData.slice(0, width * height),
        };
    }

    /**
     * 创建优化的PGM网格
     */
    createOptimizedPGMMesh(pgmData, cacheKey) {
        const { width, height, maxVal, data } = pgmData;
        const config = this.mapConfig;

        // 使用YAML配置中的分辨率计算实际尺寸
        const realWidth = width * config.resolution;
        const realHeight = height * config.resolution;

        console.log(`PGM尺寸: ${width}x${height} 像素`);
        console.log(`实际尺寸: ${realWidth.toFixed(3)}x${realHeight.toFixed(3)} 米`);
        console.log(`分辨率: ${config.resolution} 米/像素`);

        // 优化几何体分段数，避免过多顶点
        // 根据地图大小和分辨率动态调整分段数
        const mapArea = width * height;
        let maxSegments = 512; // 默认最大分段数

        // 对于大地图，进一步限制分段数以提高性能
        if (mapArea > 1000000) { // 超过100万像素
            maxSegments = 256;
            console.log('检测到大地图，降低几何体分段数以提高性能');
        } else if (mapArea > 500000) { // 超过50万像素
            maxSegments = 384;
        }

        // 根据分辨率调整分段数 - 高分辨率地图使用更少的分段
        if (config.resolution < 0.03) { // 高分辨率地图
            maxSegments = Math.min(maxSegments, 256);
            console.log('检测到高分辨率地图，进一步降低分段数');
        }

        const widthSegments = Math.min(width - 1, maxSegments);
        const heightSegments = Math.min(height - 1, maxSegments);

        console.log(`几何体分段: ${widthSegments}x${heightSegments} (原始: ${width - 1}x${height - 1}, 地图面积: ${mapArea}像素)`);

        // 创建平面几何体，并设置优化标志
        const geometry = markRaw(new THREE.PlaneGeometry(realWidth, realHeight, widthSegments, heightSegments));

        // 为大地图禁用自动法线计算以提高性能
        if (mapArea > 1000000) {
            geometry.computeVertexNormals = false;
        }

        // 创建优化的纹理
        const texture = this.createOptimizedPGMTexture(width, height, maxVal, data, config, cacheKey);

        // 创建材质，根据地图复杂度优化设置
        const materialOptions = {
            map: texture,
            transparent: true,
            side: THREE.DoubleSide,
        };

        // 为大地图添加性能优化
        if (mapArea > 1000000) {
            materialOptions.alphaTest = 0.1; // 启用alpha测试以提高性能
            materialOptions.depthWrite = false; // 禁用深度写入以减少GPU负载
        }

        const material = markRaw(new THREE.MeshBasicMaterial(materialOptions));

        // 创建网格
        const mesh = new THREE.Mesh(geometry, material);

        // 设置位置（使用YAML配置中的原点）
        // Z轴向上，Y轴水平，X轴保持不变
        // PGM地图在XY平面上，不需要旋转
        // mesh.rotation.x = -Math.PI / 2  // 移除旋转，地图直接在XY平面

        const originX = config.origin[0];
        const originY = config.origin[1];
        const centerX = originX + realWidth / 2;
        // YAML的Y坐标对应新的Y轴坐标
        const centerY = originY + realHeight / 2;

        mesh.position.set(centerX, centerY, 0.01);  // Z=0.01稍微抬高避免Z-fighting

        console.log(`地图原点: [${originX}, ${originY}]`);
        console.log(`地图中心: [${centerX}, ${centerY}]`);

        // 添加名称标识
        mesh.name = 'pgm-map';
        mesh.userData = {
            type: 'pgm',
            width: realWidth,
            height: realHeight,
            origin: config.origin,
            resolution: config.resolution,
            complexity: mapArea,
            optimized: mapArea > 1000000, // 标记是否已优化
        };

        // 为大地图设置射线检测优化
        if (mapArea > 1000000) {
            mesh.raycast = this.createOptimizedRaycast();
        }

        // 使用markRaw防止Vue响应式代理
        return markRaw(mesh);
    }

    /**
     * 使用配置创建PGM网格（保留原方法作为备用）
     */
    createPGMMeshWithConfig(pgmData) {
        return this.createOptimizedPGMMesh(pgmData, 'default');
    }

    /**
     * 使用配置计算PGM边界信息
     */
    calculatePGMBoundsWithConfig(pgmData) {
        const { width, height } = pgmData;
        const config = this.mapConfig;

        // 使用YAML配置中的分辨率和原点
        const realWidth = width * config.resolution;
        const realHeight = height * config.resolution;
        const originX = config.origin[0];
        const originY = config.origin[1];

        // 计算世界坐标系中的边界（Z轴向上，Y轴水平）
        const minX = originX;
        const maxX = originX + realWidth;
        // YAML的Y坐标对应新的Y轴坐标
        const minY = originY;
        const maxY = originY + realHeight;
        const centerX = originX + realWidth / 2;
        const centerY = originY + realHeight / 2;

        this.pgmBounds = {
            width: realWidth,
            height: realHeight,
            minX: minX,
            maxX: maxX,
            minY: minY,  // Y轴边界
            maxY: maxY,  // Y轴边界
            minZ: 0,     // Z轴最小值（地面）
            maxZ: 10,    // Z轴最大值（高度限制）
            centerX: centerX,
            centerY: centerY,  // Y轴中心
            centerZ: 5,        // Z轴中心（高度中心）
            origin: config.origin,
            resolution: config.resolution,
        };

        console.log('PGM边界（世界坐标）:', this.pgmBounds);
    }

    /**
     * 创建优化的PGM纹理（支持缓存和性能优化）
     */
    createOptimizedPGMTexture(width, height, maxVal, data, config, cacheKey) {
        // 检查缓存
        if (this.textureCache.has(cacheKey)) {
            return this.textureCache.get(cacheKey).texture;
        }

        // 优化纹理尺寸，避免过大的纹理
        // 根据地图大小和分辨率动态调整最大纹理尺寸
        const mapArea = width * height;
        let maxTextureSize = 2048; // 默认最大纹理尺寸

        // 对于超大地图，使用更小的纹理尺寸以提高性能
        if (mapArea > 2000000) { // 超过200万像素
            maxTextureSize = 1024;
            console.log('检测到超大地图，降低纹理尺寸以提高性能');
        } else if (mapArea > 1000000) { // 超过100万像素
            maxTextureSize = 1536;
        }

        // 高分辨率地图也需要限制纹理尺寸
        if (config.resolution < 0.03) {
            maxTextureSize = Math.min(maxTextureSize, 1024);
            console.log('检测到高分辨率地图，限制纹理尺寸');
        }

        let textureWidth = width;
        let textureHeight = height;
        let needsResize = false;

        if (width > maxTextureSize || height > maxTextureSize) {
            const scale = Math.min(maxTextureSize / width, maxTextureSize / height);
            textureWidth = Math.floor(width * scale);
            textureHeight = Math.floor(height * scale);
            needsResize = true;
            console.log(`纹理尺寸优化: ${width}x${height} -> ${textureWidth}x${textureHeight} (地图面积: ${mapArea}像素)`);
        }

        const canvas = document.createElement('canvas');
        canvas.width = textureWidth;
        canvas.height = textureHeight;
        const ctx = canvas.getContext('2d');

        // 使用ImageData进行高效像素操作
        const imageData = ctx.createImageData(textureWidth, textureHeight);
        const pixels = imageData.data;

        // 使用YAML配置中的阈值
        const occupiedThresh = config.occupied_thresh || 0.65;
        const freeThresh = config.free_thresh || 0.25;
        const negate = config.negate || 0;

        console.log(`使用阈值 - 占用: ${occupiedThresh}, 自由: ${freeThresh}, 反转: ${negate}`);

        // 优化的像素处理
        if (needsResize) {
            // 需要缩放时使用采样
            this.processResizedPixels(pixels, data, width, height, textureWidth, textureHeight, maxVal, occupiedThresh, freeThresh, negate);
        } else {
            // 直接处理
            this.processDirectPixels(pixels, data, maxVal, occupiedThresh, freeThresh, negate);
        }

        ctx.putImageData(imageData, 0, 0);

        const texture = markRaw(new THREE.CanvasTexture(canvas));
        texture.flipY = true;  // 翻转Y轴以匹配正确的地图方向
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;

        // 缓存纹理
        this.textureCache.set(cacheKey, {
            texture,
            bounds: this.pgmBounds,
            config: { ...config },
        });

        return texture;
    }

    /**
     * 处理直接像素数据
     */
    processDirectPixels(pixels, data, maxVal, occupiedThresh, freeThresh, negate) {
        for (let i = 0; i < data.length; i++) {
            const value = data[i] / maxVal;
            const pixelIndex = i * 4;
            const color = this.getPixelColor(value, occupiedThresh, freeThresh, negate);

            pixels[pixelIndex] = color.r;
            pixels[pixelIndex + 1] = color.g;
            pixels[pixelIndex + 2] = color.b;
            pixels[pixelIndex + 3] = 255;
        }
    }

    /**
     * 处理缩放后的像素数据
     */
    processResizedPixels(pixels, data, origWidth, origHeight, newWidth, newHeight, maxVal, occupiedThresh, freeThresh, negate) {
        const scaleX = origWidth / newWidth;
        const scaleY = origHeight / newHeight;

        for (let y = 0; y < newHeight; y++) {
            for (let x = 0; x < newWidth; x++) {
                const origX = Math.floor(x * scaleX);
                const origY = Math.floor(y * scaleY);
                const origIndex = origY * origWidth + origX;

                if (origIndex < data.length) {
                    const value = data[origIndex] / maxVal;
                    const pixelIndex = (y * newWidth + x) * 4;
                    const color = this.getPixelColor(value, occupiedThresh, freeThresh, negate);

                    pixels[pixelIndex] = color.r;
                    pixels[pixelIndex + 1] = color.g;
                    pixels[pixelIndex + 2] = color.b;
                    pixels[pixelIndex + 3] = 255;
                }
            }
        }
    }

    /**
     * 获取像素颜色
     */
    getPixelColor(value, occupiedThresh, freeThresh, negate) {
        if (negate) {
            if (value >= occupiedThresh) {
                return { r: 232, g: 232, b: 232 }; // 自由空间
            } else if (value <= freeThresh) {
                return { r: 60, g: 60, b: 60 }; // 占用空间
            } else {
                return { r: 150, g: 150, b: 150 }; // 未知区域
            }
        } else {
            if (value <= freeThresh) {
                return { r: 232, g: 232, b: 232 }; // 自由空间
            } else if (value >= occupiedThresh) {
                return { r: 60, g: 60, b: 60 }; // 占用空间
            } else {
                return { r: 150, g: 150, b: 150 }; // 未知区域
            }
        }
    }

    /**
     * 使用配置创建PGM纹理（保留原方法）
     */
    createPGMTextureWithConfig(width, height, maxVal, data, config) {
        return this.createOptimizedPGMTexture(width, height, maxVal, data, config, 'default');
    }

    /**
     * 获取PGM边界信息
     */
    getPGMBounds() {
        return this.pgmBounds;
    }

    /**
     * 检查是否已渲染PGM
     */
    hasPGM() {
        return this.pgmMesh !== null;
    }

    /**
     * 检查点是否在PGM范围内（Y轴水平）
     */
    isPointInPGMBounds(x, y) {
        if (!this.pgmBounds) return false;

        return x >= this.pgmBounds.minX &&
            x <= this.pgmBounds.maxX &&
            y >= this.pgmBounds.minY &&
            y <= this.pgmBounds.maxY;
    }

    /**
     * 将点限制在PGM范围内（Y轴水平）
     */
    clampPointToPGMBounds(x, y) {
        if (!this.pgmBounds) return { x, y };

        return {
            x: Math.max(this.pgmBounds.minX, Math.min(this.pgmBounds.maxX, x)),
            y: Math.max(this.pgmBounds.minY, Math.min(this.pgmBounds.maxY, y)),
        };
    }

    /**
     * 创建优化的射线检测方法（用于大地图）
     */
    createOptimizedRaycast() {
        return function (raycaster, intersects) {
            // 对于大地图，使用简化的射线检测
            // 只检测边界框而不是详细的几何体
            const box = new THREE.Box3().setFromObject(this);
            const ray = raycaster.ray;

            const intersectPoint = new THREE.Vector3();
            const intersectResult = ray.intersectBox(box, intersectPoint);

            if (intersectResult) {
                const distance = raycaster.ray.origin.distanceTo(intersectPoint);
                if (distance < raycaster.far && distance > raycaster.near) {
                    intersects.push({
                        distance: distance,
                        point: intersectPoint.clone(),
                        object: this,
                    });
                }
            }
        };
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.textureCache.forEach(({ texture }) => {
            if (texture && texture.dispose) {
                texture.dispose();
            }
        });
        this.textureCache.clear();
        console.log('PGM纹理缓存已清理');
    }

    /**
     * 获取缓存统计信息
     */
    getCacheStats() {
        return {
            cacheSize: this.textureCache.size,
            isRendering: this.isRendering,
            queueLength: this.renderQueue.length,
        };
    }

    /**
     * 强制清理所有PGM资源
     */
    forceCleanup() {
        console.log('强制清理PGM资源...');

        // 移除所有PGM相关的网格
        const pgmObjects = this.scene.children.filter(child =>
            child.name && (child.name.includes('pgm') || child.userData.type === 'pgm'),
        );

        pgmObjects.forEach(obj => {
            this.scene.remove(obj);
            if (obj.geometry) obj.geometry.dispose();
            if (obj.material) {
                if (obj.material.map) obj.material.map.dispose();
                obj.material.dispose();
            }
            console.log('已移除PGM对象:', obj.name);
        });

        // 清理内部状态
        this.removePreviousPGM();
        this.clearCache();
        this.pgmData = null;
        this.pgmBounds = null;
        this.mapConfig = null;
        this.renderQueue = [];
        this.isRendering = false;

        console.log('PGM资源强制清理完成');
    }

    /**
     * 清理资源
     */
    dispose() {
        this.forceCleanup();
    }
}
