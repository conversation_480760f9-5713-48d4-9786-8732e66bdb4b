import request from '../../util/request.js';

// 获取地图列表
export const getMapList = () => {
    return request({
        url: '/api-j/RobotMapResource/list',
        method: 'get',
    });
};
// 根据ID获取地图详细信息
export const getMapInfo = mapId => {
    return request({
        url: '/api-j/RobotMapResource/get/' + mapId,
        method: 'get',
    });
};

// 保存更新地图信息
export const saveMapInfo = mapInfo => {
    return request({
        url: '/api-j/RobotMapResource/update',
        method: 'put', // 尝试使用PUT方法，通常更新操作使用PUT
        data: mapInfo,
    });
};

// 根据地图 ID 删除地图
export const deleteMapById = mapId => {
    return request({
        url: '/api-j/RobotMapResource/delete/' + mapId,
        method: 'delete',
    });
};

// 根据地图 ID 和 文件名 下载地图文件
export const downloadMapFile = (mapId, fileName) => {
    return request({
        url: `/api-j/RobotMapResource/downloadFile?mapId=${mapId}&fileName=${fileName}`,
        method: 'get',
        responseType: 'blob', // 指定响应类型为blob，用于文件下载
        timeout: 900000, // 15分钟超时，适用于大文件下载
    });
};

// 上传地图文件
export const uploadMapFile = (mapId, files) => {
    const formData = new FormData();
    formData.append('mapId', mapId);

    // 添加文件到FormData
    if (files && files.length > 0) {
        for (let i = 0; i < files.length; i++) {
            formData.append('files', files[i]);
        }
    }

    return request({
        url: '/api-j/RobotMapResource/uploadFile',
        method: 'post',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        timeout: 900000, // 15分钟超时，适用于大文件上传
    });
};
