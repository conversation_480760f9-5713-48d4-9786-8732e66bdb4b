import request from '../../util/request.js';

// 创建路径（单个）
export const createPath = pathInfo => {
    return request({
        url: '/api-j/RobotMapPathResource/create',
        method: 'post',
        data: pathInfo,
    });
};

// 更新路径（单个）
export const updatePath = pathInfo => {
    return request({
        url: '/api-j/RobotMapPathResource/update',
        method: 'post',
        data: pathInfo,
    });
};

// 删除路径（单个）
export const deletePath = pathInfo => {
    return request({
        url: '/api-j/RobotMapPathResource/delete',
        method: 'post',
        data: pathInfo,
    });
};

// 根据uuid查询路径
export const getPathByUuid = (mapId, uuid) => {
    return request({
        url: `/api-j/RobotMapPathResource/get?mapId=${mapId}&uuid=${uuid}`,
        method: 'get',
    });
};

// 根据mapId查询路径列表
export const getPathList = mapId => {
    return request({
        url: `/api-j/RobotMapPathResource/list?mapId=${mapId}`,
        method: 'get',
    });
};