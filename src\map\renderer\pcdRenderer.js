/**
 * PCD文件渲染工具模块
 * 处理PCD文件的加载、解析和渲染
 */

import { markRaw } from 'vue';
import * as THREE from 'three';
import { message } from 'ant-design-vue';

/**
 * PCD渲染器类 - 性能优化版本
 */
export class PCDRenderer {
    constructor(scene) {
        this.scene = scene;
        this.pcdMesh = null;
        this.pcdData = null; // 存储PCD数据信息
        this.pcdBounds = null; // 存储PCD边界信息

        // 性能优化相关
        this.lodLevels = []; // LOD层级数据
        this.currentLOD = 0; // 当前LOD层级
        this.isRendering = false; // 渲染状态标志
        this.pointCache = new Map(); // 点云数据缓存
        this.maxPoints = 1000000; // 最大点数限制
        this.lodDistances = [50, 100, 200, 500]; // LOD距离阈值
    }

    /**
     * 检查PCD文件格式
     */
    async checkPCDFile(filePath) {
        try {
            const response = await fetch(filePath);
            if (!response.ok) {
                throw new Error(`无法加载PCD文件: ${response.status}`);
            }

            const arrayBuffer = await response.arrayBuffer();
            const uint8Array = new Uint8Array(arrayBuffer);

            console.log('文件大小:', arrayBuffer.byteLength, '字节');

            // 显示文件前500字节的内容
            const firstBytes = Array.from(uint8Array.slice(0, 500))
                .map(b => b >= 32 && b <= 126 ? String.fromCharCode(b) : `[${b}]`)
                .join('');

            console.log('文件前500字节内容:');
            console.log(firstBytes);

            // 查找DATA行
            const decoder = new TextDecoder('utf-8', { fatal: false });
            const textPortion = decoder.decode(uint8Array.slice(0, Math.min(uint8Array.length, 2000)));
            const lines = textPortion.split(/[\r\n]+/);

            console.log('文件前20行:');
            lines.slice(0, 20).forEach((line, index) => {
                console.log(`${index + 1}: ${line}`);
            });

            return { success: true, lines: lines.slice(0, 20) };
        } catch (error) {
            console.error('检查PCD文件失败:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 渲染PCD文件 - 性能优化版本
     */
    async renderPCD(filePath = null, scale = 1.0, pointSize = 0.05) {
        if (!filePath) {
            message.warning('未找到此地图的PCD文件，无法渲染地图。');
        }
        // 防止重复渲染
        if (this.isRendering) {
            console.warn('PCD正在渲染中，跳过重复请求');
            return;
        }

        this.isRendering = true;

        try {
            // 检查缓存
            const cacheKey = `${filePath}_${scale}_${pointSize}`;
            if (this.pointCache.has(cacheKey)) {
                console.log('使用缓存的PCD数据');
                return this.renderFromCache(cacheKey);
            }

            // 首先检查文件格式
            console.log('开始检查PCD文件格式...');
            await this.checkPCDFile(filePath);

            // 移除之前的PCD网格
            this.removePreviousPCD();

            // 加载PCD文件
            console.log('开始解析PCD文件...');
            const pcdData = await this.loadPCDFile(filePath);
            this.pcdData = pcdData;

            // 创建LOD层级数据
            this.createLODLevels(pcdData);

            // 创建优化的PCD点云
            this.pcdMesh = this.createOptimizedPCDPointCloud(pcdData, scale, pointSize, cacheKey);

            // 计算PCD边界信息
            this.calculatePCDBounds(pcdData, scale);

            // 添加到场景
            this.scene.add(this.pcdMesh);

            console.log('PCD文件渲染完成:', pcdData);
            console.log('PCD边界信息:', this.pcdBounds);
            console.log('LOD层级数:', this.lodLevels.length);

            return {
                success: true,
                data: pcdData,
                bounds: this.pcdBounds,
                mesh: this.pcdMesh,
                lodLevels: this.lodLevels.length,
            };
        } catch (error) {
            console.error('PCD渲染错误:', error);
            throw error;
        } finally {
            this.isRendering = false;
        }
    }

    /**
     * 从缓存渲染PCD
     */
    renderFromCache(cacheKey) {
        const cachedData = this.pointCache.get(cacheKey);
        if (!cachedData) return null;

        // 移除之前的网格
        this.removePreviousPCD();

        // 使用缓存的数据
        this.pcdData = cachedData.pcdData;
        this.pcdBounds = cachedData.bounds;
        this.lodLevels = cachedData.lodLevels;

        // 重新创建点云（使用最高LOD）
        const highestLOD = this.lodLevels[0] || { points: this.pcdData.points };
        this.pcdMesh = this.createPointCloudFromLOD(highestLOD, cachedData.scale, cachedData.pointSize);

        this.scene.add(this.pcdMesh);

        return {
            success: true,
            data: this.pcdData,
            bounds: this.pcdBounds,
            mesh: this.pcdMesh,
            lodLevels: this.lodLevels.length,
        };
    }

    /**
     * 创建LOD层级数据
     */
    createLODLevels(pcdData) {
        const { points } = pcdData;
        this.lodLevels = [];

        // 创建多个LOD层级
        const lodRatios = [1.0, 0.5, 0.25, 0.1]; // 100%, 50%, 25%, 10%

        lodRatios.forEach((ratio, index) => {
            const targetCount = Math.floor(points.length * ratio);
            const lodPoints = this.samplePoints(points, targetCount);

            this.lodLevels.push({
                level: index,
                ratio: ratio,
                points: lodPoints,
                pointCount: lodPoints.length,
            });

            console.log(`LOD ${index}: ${lodPoints.length} 点 (${(ratio * 100).toFixed(1)}%)`);
        });
    }

    /**
     * 采样点云数据
     */
    samplePoints(points, targetCount) {
        if (points.length <= targetCount) {
            return [...points];
        }

        const step = points.length / targetCount;
        const sampledPoints = [];

        for (let i = 0; i < targetCount; i++) {
            const index = Math.floor(i * step);
            if (index < points.length) {
                sampledPoints.push(points[index]);
            }
        }

        return sampledPoints;
    }

    /**
     * 移除之前的PCD网格
     */
    removePreviousPCD() {
        if (this.pcdMesh) {
            this.scene.remove(this.pcdMesh);
            // 清理资源
            if (this.pcdMesh.geometry) {
                this.pcdMesh.geometry.dispose();
            }
            if (this.pcdMesh.material) {
                this.pcdMesh.material.dispose();
            }
            this.pcdMesh = null;
        }
    }

    /**
     * 加载PCD文件
     */
    async loadPCDFile(filePath) {
        const response = await fetch(filePath);
        if (!response.ok) {
            throw new Error(`无法加载PCD文件: ${response.status}`);
        }

        const arrayBuffer = await response.arrayBuffer();
        return this.parsePCD(arrayBuffer);
    }

    /**
     * 解析PCD文件
     */
    parsePCD(arrayBuffer) {
        const uint8Array = new Uint8Array(arrayBuffer);

        // 解析头部
        const { headerText, headerEnd } = this.extractPCDHeader(uint8Array);
        const header = this.parsePCDHeader(headerText);

        // 解析点云数据
        const points = this.parsePCDData(uint8Array, headerEnd, header);

        return {
            header,
            points,
            pointCount: points.length,
        };
    }

    /**
     * 提取PCD文件头部
     */
    extractPCDHeader(uint8Array) {
        // 方法1: 使用TextDecoder快速解码前部分
        const decoder = new TextDecoder('utf-8', { fatal: false });
        const maxHeaderSize = Math.min(uint8Array.length, 10000);
        const textPortion = decoder.decode(uint8Array.slice(0, maxHeaderSize));

        const lines = textPortion.split(/[\r\n]+/);
        let dataLineIndex = -1;

        for (let i = 0; i < lines.length; i++) {
            if (lines[i].trim().startsWith('DATA ')) {
                dataLineIndex = i;
                break;
            }
        }

        if (dataLineIndex !== -1) {
            const headerText = lines.slice(0, dataLineIndex + 1).join('\n') + '\n';
            const headerBytes = new TextEncoder().encode(headerText);
            return { headerText, headerEnd: headerBytes.length };
        }

        // 方法2: 字节级搜索DATA标记
        return this.findDataMarkerByBytes(uint8Array);
    }

    /**
     * 通过字节搜索找到DATA标记
     */
    findDataMarkerByBytes(uint8Array) {
        const dataPattern = new Uint8Array([68, 65, 84, 65, 32]); // "DATA "

        for (let i = 0; i <= uint8Array.length - dataPattern.length; i++) {
            if (this.matchPattern(uint8Array, i, dataPattern)) {
                // 找到行结束位置
                let lineEnd = i + dataPattern.length;
                while (lineEnd < uint8Array.length &&
                    uint8Array[lineEnd] !== 10 && uint8Array[lineEnd] !== 13) {
                    lineEnd++;
                }

                const headerText = new TextDecoder().decode(uint8Array.slice(0, lineEnd + 1));
                return { headerText, headerEnd: lineEnd + 1 };
            }
        }

        throw new Error('无法找到PCD文件的DATA行，可能文件格式不正确');
    }

    /**
     * 匹配字节模式
     */
    matchPattern(uint8Array, startIndex, pattern) {
        for (let j = 0; j < pattern.length; j++) {
            if (uint8Array[startIndex + j] !== pattern[j]) {
                return false;
            }
        }
        return true;
    }

    /**
     * 解析PCD头部信息
     */
    parsePCDHeader(headerText) {
        const lines = headerText.split(/[\r\n]+/);
        const header = {};

        // 头部字段解析映射
        const fieldParsers = {
            version: parts => parts[1],
            fields: parts => parts.slice(1),
            size: parts => parts.slice(1).map(Number),
            type: parts => parts.slice(1),
            count: parts => parts.slice(1).map(Number),
            width: parts => parseInt(parts[1]),
            height: parts => parseInt(parts[1]),
            viewpoint: parts => parts.slice(1).map(Number),
            points: parts => parseInt(parts[1]),
        };

        for (const line of lines) {
            const trimmedLine = line.trim();

            // 处理DATA行
            if (trimmedLine.startsWith('DATA ')) {
                const dataParts = trimmedLine.split(/\s+/);
                if (dataParts.length >= 2) {
                    header.dataType = dataParts[1].toLowerCase();
                }
                break;
            }

            // 跳过注释和空行
            if (trimmedLine.startsWith('#') || trimmedLine === '') continue;

            const parts = trimmedLine.split(/\s+/);
            if (parts.length >= 2) {
                const key = parts[0].toLowerCase();
                const parser = fieldParsers[key];

                if (parser) {
                    try {
                        header[key] = parser(parts);
                    } catch (e) {
                        console.warn(`解析头部字段 ${key} 时出错:`, e);
                    }
                }
            }
        }

        // 验证和设置默认值
        this.validateAndSetDefaults(header);

        return header;
    }

    /**
     * 验证头部信息并设置默认值
     */
    validateAndSetDefaults(header) {
        if (!header.dataType) {
            throw new Error('未找到DATA行或数据类型');
        }

        if (!header.fields || header.fields.length === 0) {
            console.warn('未找到FIELDS信息，使用默认字段 [x, y, z]');
            header.fields = ['x', 'y', 'z'];
        }

        if (!header.points || header.points <= 0) {
            console.warn('未找到有效的POINTS信息');
        }
    }

    /**
     * 解析PCD点云数据
     */
    parsePCDData(uint8Array, headerEnd, header) {
        if (header.dataType === 'ascii') {
            return this.parseASCIIData(uint8Array, headerEnd, header);
        } else if (header.dataType === 'binary') {
            return this.parseBinaryPCDData(uint8Array, headerEnd, header);
        } else {
            throw new Error(`不支持的PCD数据格式: ${header.dataType}`);
        }
    }

    /**
     * 解析ASCII格式数据
     */
    parseASCIIData(uint8Array, headerEnd, header) {
        // 从headerEnd位置开始读取ASCII数据
        const decoder = new TextDecoder('utf-8');
        const dataText = decoder.decode(uint8Array.slice(headerEnd));
        const dataLines = dataText.split('\n').filter(line => line.trim() !== '');

        console.log(`开始解析ASCII数据，共 ${dataLines.length} 行`);

        const points = [];
        const maxLines = Math.min(dataLines.length, 50000);
        const colorIndex = this.getColorFieldIndex(header.fields);

        for (let i = 0; i < maxLines; i++) {
            const point = this.parseASCIILine(dataLines[i], colorIndex);
            if (point) {
                points.push(point);

                if (points.length % 10000 === 0) {
                    console.log(`已解析 ${points.length} 个有效点`);
                }
            }
        }

        console.log(`ASCII解析完成，共解析 ${points.length} 个有效点`);
        return points;
    }

    /**
     * 获取颜色字段索引
     */
    getColorFieldIndex(fields) {
        if (!fields) return -1;
        const rgbIndex = fields.indexOf('rgb');
        const rgbaIndex = fields.indexOf('rgba');
        return rgbIndex !== -1 ? rgbIndex : rgbaIndex;
    }

    /**
     * 解析ASCII数据行
     */
    parseASCIILine(line, colorIndex) {
        try {
            const values = line.trim().split(/\s+/);
            if (values.length < 3) return null;

            const x = parseFloat(values[0]);
            const y = parseFloat(values[1]);
            const z = parseFloat(values[2]);

            if (!isFinite(x) || !isFinite(y) || !isFinite(z)) return null;

            const point = { x, y, z };

            // 解析颜色信息
            if (colorIndex !== -1 && values[colorIndex] !== undefined) {
                const colorValue = parseFloat(values[colorIndex]);
                if (isFinite(colorValue)) {
                    point.color = this.parseRGBValue(colorValue);
                }
            }

            return point;
        } catch (error) {
            return null;
        }
    }

    /**
     * 解析二进制PCD数据
     */
    parseBinaryPCDData(uint8Array, headerEnd, header) {
        if (headerEnd >= uint8Array.length) {
            throw new Error('头部结束位置超出文件大小');
        }

        const dataView = new DataView(uint8Array.buffer, headerEnd);
        const availableBytes = uint8Array.length - headerEnd;
        const pointSize = this.calculatePointSize(header);
        const actualPointCount = Math.min(
            Math.floor(availableBytes / pointSize),
            header.points || Infinity,
            500000, // 最多50万个点
        );

        console.log(`开始解析二进制PCD数据: 点大小=${pointSize}字节, 目标点数=${actualPointCount}`);

        const points = [];
        const batchSize = 10000;
        const hasRGB = header.fields && header.fields.includes('rgb');

        for (let batchStart = 0; batchStart < actualPointCount; batchStart += batchSize) {
            const batchEnd = Math.min(batchStart + batchSize, actualPointCount);

            for (let i = batchStart; i < batchEnd; i++) {
                const point = this.parseBinaryPoint(dataView, i * pointSize, hasRGB, availableBytes);
                if (point) {
                    points.push(point);
                }
            }

            if (batchStart % 50000 === 0) {
                console.log(`已处理 ${points.length} / ${actualPointCount} 个点`);
            }
        }

        console.log(`二进制解析完成，共解析 ${points.length} 个有效点`);
        return points;
    }

    /**
     * 计算每个点的字节大小
     */
    calculatePointSize(header) {
        if (header.size && header.fields && header.size.length === header.fields.length) {
            return header.size.reduce((total, size, index) => {
                const count = header.count ? header.count[index] : 1;
                return total + size * count;
            }, 0);
        }
        return 16; // 默认值：x,y,z,rgb 各4字节
    }

    /**
     * 解析单个二进制点
     */
    parseBinaryPoint(dataView, offset, hasRGB, availableBytes) {
        if (offset + 12 > availableBytes) return null;

        try {
            const point = {
                x: dataView.getFloat32(offset, true),
                y: dataView.getFloat32(offset + 4, true),
                z: dataView.getFloat32(offset + 8, true),
            };

            // 验证点的有效性
            if (!isFinite(point.x) || !isFinite(point.y) || !isFinite(point.z)) {
                return null;
            }

            // 解析RGB数据
            if (hasRGB && offset + 16 <= availableBytes) {
                try {
                    const rgbValue = dataView.getUint32(offset + 12, true);
                    if (rgbValue !== 0) {
                        point.color = this.parseRGBValue(rgbValue);
                    }
                } catch (e) {
                    // RGB解析失败时忽略
                }
            }

            return point;
        } catch (e) {
            return null;
        }
    }

    /**
     * 解析RGB值
     */
    parseRGBValue(rgbValue) {
        // PCD文件中的RGB通常是打包的32位整数
        const rgb = Math.floor(rgbValue);
        const r = (rgb >> 16) & 0xFF;
        const g = (rgb >> 8) & 0xFF;
        const b = rgb & 0xFF;
        return { r: r / 255, g: g / 255, b: b / 255 };
    }

    /**
     * 创建优化的PCD点云
     */
    createOptimizedPCDPointCloud(pcdData, scale = 1.0, pointSize = 0.1, cacheKey) {
        const { points } = pcdData;

        if (points.length === 0) {
            throw new Error('PCD文件中没有有效的点数据');
        }

        console.log(`开始创建优化点云，共 ${points.length} 个点`);

        // 使用最高质量的LOD层级
        const highestLOD = this.lodLevels[0] || { points: points };
        const pointCloud = this.createPointCloudFromLOD(highestLOD, scale, pointSize);

        // 缓存数据
        this.pointCache.set(cacheKey, {
            pcdData: pcdData,
            bounds: this.pcdBounds,
            lodLevels: this.lodLevels,
            scale: scale,
            pointSize: pointSize,
        });

        return pointCloud;
    }

    /**
     * 从LOD数据创建点云
     */
    createPointCloudFromLOD(lodData, scale = 1.0, pointSize = 0.1) {
        const { points } = lodData;

        // 限制点数防止内存溢出
        const maxPoints = Math.min(points.length, this.maxPoints);
        const actualPoints = points.slice(0, maxPoints);

        if (maxPoints < points.length) {
            console.warn(`点数过多，已限制为 ${maxPoints} 个点（原始: ${points.length}）`);
        }

        // 创建几何体
        const geometry = markRaw(new THREE.BufferGeometry());

        // 优化的数组创建
        const totalPoints = actualPoints.length;
        const positions = new Float32Array(totalPoints * 3);
        const colors = new Float32Array(totalPoints * 3);

        console.log('开始处理点数据...');

        // 优化的点数据处理
        this.processPointData(actualPoints, positions, colors, scale);

        console.log('点数据处理完成，设置几何体属性...');

        // 设置几何体属性
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        // 创建材质
        const material = markRaw(new THREE.PointsMaterial({
            size: pointSize,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            sizeAttenuation: true,
        }));

        // 创建点云对象
        const pointCloud = new THREE.Points(geometry, material);

        // 添加名称标识
        pointCloud.name = 'pcd-point-cloud';
        pointCloud.userData = {
            type: 'pcd',
            pointCount: actualPoints.length,
            scale: scale,
            pointSize: pointSize,
            lodLevel: lodData.level || 0,
        };

        console.log(`点云创建完成，实际点数: ${actualPoints.length}`);

        // 使用markRaw防止Vue响应式代理
        return markRaw(pointCloud);
    }

    /**
     * 优化的点数据处理
     */
    processPointData(points, positions, colors, scale) {
        const batchSize = 10000;
        const totalPoints = points.length;

        // 分批处理点数据
        for (let batchStart = 0; batchStart < totalPoints; batchStart += batchSize) {
            const batchEnd = Math.min(batchStart + batchSize, totalPoints);

            for (let i = batchStart; i < batchEnd; i++) {
                const point = points[i];
                const index = i * 3;

                // 坐标系转换（Z轴向上，Y轴水平）
                const x = point.x * scale;     // X轴保持不变
                const y = point.y * scale;     // PCD的Y轴对应新的Y轴（不翻转，与PGM保持一致）
                const z = point.z * scale;     // PCD的Z轴对应新的Z轴（高度）

                positions[index] = x;
                positions[index + 1] = y;
                positions[index + 2] = z;

                // 设置颜色
                if (point.color) {
                    colors[index] = point.color.r;
                    colors[index + 1] = point.color.g;
                    colors[index + 2] = point.color.b;
                } else {
                    // 默认颜色：根据高度设置渐变色
                    const height = point.z * scale;
                    colors[index] = 0.5 + Math.min(height * 0.1, 0.5);
                    colors[index + 1] = 0.8;
                    colors[index + 2] = 0.3 + Math.min(height * 0.1, 0.5);
                }
            }

            // 输出进度
            if (batchStart % 50000 === 0) {
                console.log(`已处理 ${Math.min(batchEnd, totalPoints)} / ${totalPoints} 个点`);
            }
        }
    }

    /**
     * 创建PCD点云（保留原方法）
     */
    createPCDPointCloud(pcdData, scale = 1.0, pointSize = 0.1) {
        return this.createOptimizedPCDPointCloud(pcdData, scale, pointSize, 'default');
    }

    /**
     * 计算PCD边界信息
     */
    calculatePCDBounds(pcdData, scale = 1.0) {
        const { points } = pcdData;

        if (points.length === 0) {
            this.pcdBounds = null;
            return;
        }

        let minX = Infinity, maxX = -Infinity;
        let minY = Infinity, maxY = -Infinity;
        let minZ = Infinity, maxZ = -Infinity;

        points.forEach(point => {
            const x = point.x * scale;     // X轴保持不变
            const y = point.y * scale;     // PCD的Y轴对应新的Y轴（不翻转，与PGM保持一致）
            const z = point.z * scale;     // PCD的Z轴对应新的Z轴（高度）

            minX = Math.min(minX, x);
            maxX = Math.max(maxX, x);
            minY = Math.min(minY, y);
            maxY = Math.max(maxY, y);
            minZ = Math.min(minZ, z);
            maxZ = Math.max(maxZ, z);
        });

        // 3D边界信息
        this.pcdBounds = {
            minX, maxX,
            minY, maxY,
            minZ, maxZ,
            width: maxX - minX,
            height: maxY - minY,  // 实际的3D高度
            depth: maxZ - minZ,   // 实际的3D深度
            centerX: (minX + maxX) / 2,
            centerY: (minY + maxY) / 2,
            centerZ: (minZ + maxZ) / 2,
        };

        console.log('PCD边界（3D）:', this.pcdBounds);
        console.log('PCD点云范围 - X:', minX.toFixed(2), '到', maxX.toFixed(2), '宽度:', (maxX - minX).toFixed(2));
        console.log('PCD点云范围 - Y:', minY.toFixed(2), '到', maxY.toFixed(2), '高度:', (maxY - minY).toFixed(2));
        console.log('PCD点云范围 - Z:', minZ.toFixed(2), '到', maxZ.toFixed(2), '深度:', (maxZ - minZ).toFixed(2));
        console.log('PCD中心点:', ((minX + maxX) / 2).toFixed(2), ((minY + maxY) / 2).toFixed(2), ((minZ + maxZ) / 2).toFixed(2));
    }

    /**
     * 获取PCD边界信息
     */
    getPCDBounds() {
        return this.pcdBounds;
    }

    /**
     * 检查是否已渲染PCD
     */
    hasPCD() {
        return this.pcdMesh !== null;
    }

    /**
     * 检查点是否在PCD范围内（Y轴水平）
     */
    isPointInPCDBounds(x, y) {
        if (!this.pcdBounds) return false;

        return x >= this.pcdBounds.minX &&
            x <= this.pcdBounds.maxX &&
            y >= this.pcdBounds.minY &&
            y <= this.pcdBounds.maxY;
    }

    /**
     * 将点限制在PCD范围内（Y轴水平）
     */
    clampPointToPCDBounds(x, y) {
        if (!this.pcdBounds) return { x, y };

        return {
            x: Math.max(this.pcdBounds.minX, Math.min(this.pcdBounds.maxX, x)),
            y: Math.max(this.pcdBounds.minY, Math.min(this.pcdBounds.maxY, y)),
        };
    }

    /**
     * 更新点云材质属性
     */
    updatePointCloudMaterial(options = {}) {
        if (!this.pcdMesh || !this.pcdMesh.material) return;

        const material = this.pcdMesh.material;

        if (options.pointSize !== undefined) {
            material.size = options.pointSize;
            this.pcdMesh.userData.pointSize = options.pointSize;
        }

        if (options.opacity !== undefined) {
            material.opacity = options.opacity;
        }

        if (options.transparent !== undefined) {
            material.transparent = options.transparent;
        }

        material.needsUpdate = true;
        console.log('点云材质已更新:', options);
    }

    /**
     * 设置点云可见性
     */
    setVisible(visible) {
        if (this.pcdMesh) {
            this.pcdMesh.visible = visible;
        }
    }

    /**
     * 根据相机距离切换LOD
     */
    updateLOD(camera) {
        if (!this.pcdMesh || !this.pcdBounds || this.lodLevels.length === 0) return;

        // 计算相机到点云中心的距离
        const distance = camera.position.distanceTo(
            new THREE.Vector3(this.pcdBounds.centerX, this.pcdBounds.centerY, this.pcdBounds.centerZ),
        );

        // 根据距离确定LOD层级
        let targetLOD = 0;
        for (let i = 0; i < this.lodDistances.length; i++) {
            if (distance > this.lodDistances[i]) {
                targetLOD = Math.min(i + 1, this.lodLevels.length - 1);
            }
        }

        // 如果LOD层级发生变化，更新点云
        if (targetLOD !== this.currentLOD) {
            this.switchToLOD(targetLOD);
        }
    }

    /**
     * 切换到指定LOD层级
     */
    switchToLOD(lodLevel) {
        if (lodLevel < 0 || lodLevel >= this.lodLevels.length) return;

        const lodData = this.lodLevels[lodLevel];
        const oldMesh = this.pcdMesh;

        // 获取当前材质参数
        const pointSize = oldMesh.userData.pointSize || 0.1;
        const scale = oldMesh.userData.scale || 1.0;

        // 创建新的点云
        const newMesh = this.createPointCloudFromLOD(lodData, scale, pointSize);

        // 替换场景中的网格
        this.scene.remove(oldMesh);
        this.scene.add(newMesh);

        // 清理旧网格
        if (oldMesh.geometry) oldMesh.geometry.dispose();
        if (oldMesh.material) oldMesh.material.dispose();

        // 更新引用
        this.pcdMesh = newMesh;
        this.currentLOD = lodLevel;

        console.log(`切换到LOD ${lodLevel}，点数: ${lodData.pointCount}`);
    }

    /**
     * 设置LOD距离阈值
     */
    setLODDistances(distances) {
        this.lodDistances = [...distances];
        console.log('LOD距离阈值已更新:', this.lodDistances);
    }

    /**
     * 设置最大点数限制
     */
    setMaxPoints(maxPoints) {
        this.maxPoints = maxPoints;
        console.log('最大点数限制已更新:', this.maxPoints);
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.pointCache.clear();
        console.log('PCD点云缓存已清理');
    }

    /**
     * 获取缓存统计信息
     */
    getCacheStats() {
        return {
            cacheSize: this.pointCache.size,
            isRendering: this.isRendering,
            currentLOD: this.currentLOD,
            lodLevels: this.lodLevels.length,
            maxPoints: this.maxPoints,
        };
    }

    /**
     * 获取点云统计信息
     */
    getStatistics() {
        if (!this.pcdData || !this.pcdBounds) return null;

        return {
            pointCount: this.pcdData.pointCount,
            bounds: this.pcdBounds,
            header: this.pcdData.header,
            hasColors: this.pcdData.points.some(p => p.color),
            memoryUsage: this.pcdData.pointCount * 6 * 4, // 位置(3) + 颜色(3) * 4字节
            currentLOD: this.currentLOD,
            lodLevels: this.lodLevels.map(lod => ({
                level: lod.level,
                pointCount: lod.pointCount,
                ratio: lod.ratio,
            })),
        };
    }

    /**
     * 强制清理所有PCD资源
     */
    forceCleanup() {
        console.log('强制清理PCD资源...');

        // 移除所有PCD相关的网格
        const pcdObjects = this.scene.children.filter(child =>
            child.name && (child.name.includes('pcd') || child.userData.type === 'pcd'),
        );

        pcdObjects.forEach(obj => {
            this.scene.remove(obj);
            if (obj.geometry) obj.geometry.dispose();
            if (obj.material) obj.material.dispose();
            console.log('已移除PCD对象:', obj.name);
        });

        // 清理内部状态
        this.removePreviousPCD();
        this.clearCache();
        this.pcdData = null;
        this.pcdBounds = null;
        this.lodLevels = [];
        this.currentLOD = 0;
        this.isRendering = false;

        console.log('PCD资源强制清理完成');
    }

    /**
     * 清理资源
     */
    dispose() {
        this.forceCleanup();
    }
}
