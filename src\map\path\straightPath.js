/**
 * 直线路径类
 */
import * as THREE from 'three';
import { BasePath, PATH_DIRECTIONS } from './pathSystem.js';

export class StraightPath extends BasePath {
    constructor(name, mapId, startPoint = { x: 0, y: 0, z: 0 }, endPoint = { x: 2, y: 0, z: 0 }) {
        super('straight', name, mapId);

        // 路径点（限制在XY平面，z=0.1避免与地板重叠）
        this.startPoint = { x: startPoint.x, y: startPoint.y, z: 0.1 };
        this.endPoint = { x: endPoint.x, y: endPoint.y, z: 0.1 };

        // 控制点对象（用于拖拽）
        this.startControlPoint = null;
        this.endControlPoint = null;

        this.createPath();
    }

    // 创建路径
    createPath() {
        this.group.clear();

        // 创建线条
        this.createLine();

        // 创建控制点
        this.createControlPoints();

        // 创建方向指示器
        this.createDirectionIndicator();
    }

    // 创建线条
    createLine() {
        const startPoint = new THREE.Vector3(this.startPoint.x, this.startPoint.y, this.startPoint.z);
        const endPoint = new THREE.Vector3(this.endPoint.x, this.endPoint.y, this.endPoint.z);

        // 计算方向和距离
        const direction = new THREE.Vector3().subVectors(endPoint, startPoint);

        direction.normalize();

        // 不调整线条长度，让箭头直接贴在线条端点上
        // 这样可以确保箭头与线条完美连接
        const adjustedDistance = startPoint.distanceTo(endPoint);
        const center = new THREE.Vector3().addVectors(startPoint, endPoint).multiplyScalar(0.5);

        // 创建可视的2D路径几何体 - 使用扁平的BoxGeometry
        const visualLineDepth = 0.04; // 可视线条深度（Y方向）
        const visualLineHeight = 0.01; // 可视线条高度（Z方向，很薄保持2D效果）
        const visualGeometry = new THREE.BoxGeometry(adjustedDistance, visualLineDepth, visualLineHeight);

        const material = new THREE.MeshBasicMaterial({
            color: this.getLineColor(),
        });

        this.line = new THREE.Mesh(visualGeometry, material);

        // 设置位置
        this.line.position.copy(center);
        // 确保Z坐标严格限制在0.1
        this.line.position.z = 0.1;

        // 设置旋转，让路径指向正确方向
        // 对于XY平面路径，需要绕Z轴旋转
        const angle = Math.atan2(direction.y, direction.x);
        this.line.rotation.set(0, 0, angle);

        this.line.userData = {
            objectType: 'pathLine',
            pathUuid: this.uuid,
        };

        // 创建不可见的碰撞检测几何体 - 更粗的盒子用于点击检测
        const collisionLineDepth = visualLineDepth * 4; // 碰撞检测深度是可视深度的4倍
        const collisionLineHeight = visualLineHeight * 4; // 碰撞检测高度也增加
        const collisionGeometry = new THREE.BoxGeometry(adjustedDistance, collisionLineDepth, collisionLineHeight);

        // 不可见材质
        const collisionMaterial = new THREE.MeshBasicMaterial({
            transparent: true,
            opacity: 0,
            visible: false, // 完全不可见
        });

        this.collisionMesh = new THREE.Mesh(collisionGeometry, collisionMaterial);
        this.collisionMesh.position.copy(center);
        this.collisionMesh.position.z = 0.1;
        this.collisionMesh.rotation.set(0, 0, angle);
        this.collisionMesh.userData = {
            objectType: 'pathLineCollision',
            pathUuid: this.uuid,
            clickable: true,
            isCollisionMesh: true, // 标记为碰撞检测网格
        };

        this.group.add(this.line);
        this.group.add(this.collisionMesh); // 添加碰撞检测网格
    }

    // 创建控制点
    createControlPoints() {
        // 起点控制点
        this.startControlPoint = this.createControlPoint(
            this.startPoint,
            0x00ff00, // 绿色
            'startPoint',
        );

        // 终点控制点
        this.endControlPoint = this.createControlPoint(
            this.endPoint,
            0xff0000, // 红色
            'endPoint',
        );

        this.group.add(this.startControlPoint);
        this.group.add(this.endControlPoint);
    }

    // 创建单个控制点
    createControlPoint(position, color, pointType) {
        // 根据地图复杂度动态调整控制点大小，提高可选择性
        const baseRadius = 0.2;
        const radius = baseRadius * (this.mapComplexity > 1000000 ? 1.5 : 1.0); // 大地图使用更大的控制点

        const geometry = new THREE.SphereGeometry(radius, 16, 16);
        const material = new THREE.MeshBasicMaterial({
            color: color,
            transparent: true,
            opacity: 0.8, // 半透明以减少视觉干扰
        });
        const sphere = new THREE.Mesh(geometry, material);

        // 确保控制点严格限制在XY平面上
        sphere.position.set(position.x, position.y, 0.1);
        sphere.userData = {
            draggable: true,
            objectType: 'pathControlPoint',
            pathUuid: this.uuid,
            pointType: pointType,
        };

        return sphere;
    }

    // 创建方向指示器
    createDirectionIndicator() {
        // 清除现有的箭头
        this.clearArrows();

        // 如果有绑定的位置A和位置B，显示端点箭头和线上箭头
        if (this.positionA && this.positionB) {
            // 端点箭头（保留原有逻辑）
            if (this.direction === PATH_DIRECTIONS.A_TO_B) {
                const arrow = this.createArrow(this.endPoint, this.startPoint);
                arrow.userData.arrowType = 'end';
                this.group.add(arrow);
            } else if (this.direction === PATH_DIRECTIONS.B_TO_A) {
                const arrow = this.createArrow(this.startPoint, this.endPoint);
                arrow.userData.arrowType = 'start';
                this.group.add(arrow);
            } else if (this.direction === PATH_DIRECTIONS.BIDIRECTIONAL) {
                const arrow1 = this.createArrow(this.endPoint, this.startPoint);
                const arrow2 = this.createArrow(this.startPoint, this.endPoint);
                arrow1.userData.arrowType = 'end';
                arrow2.userData.arrowType = 'start';
                this.group.add(arrow1);
                this.group.add(arrow2);
            }

            // 线上方向箭头（新增）
            this.createInlineDirectionArrows();
        } else {
            // 如果没有绑定位置，只显示端点箭头（原有逻辑）
            if (this.direction === PATH_DIRECTIONS.A_TO_B) {
                const arrow = this.createArrow(this.endPoint, this.startPoint);
                arrow.userData.arrowType = 'end';
                this.group.add(arrow);
            } else if (this.direction === PATH_DIRECTIONS.B_TO_A) {
                const arrow = this.createArrow(this.startPoint, this.endPoint);
                arrow.userData.arrowType = 'start';
                this.group.add(arrow);
            } else if (this.direction === PATH_DIRECTIONS.BIDIRECTIONAL) {
                const arrow1 = this.createArrow(this.endPoint, this.startPoint);
                const arrow2 = this.createArrow(this.startPoint, this.endPoint);
                arrow1.userData.arrowType = 'end';
                arrow2.userData.arrowType = 'start';
                this.group.add(arrow1);
                this.group.add(arrow2);
            }
        }
    }

    // 创建线上方向箭头
    createInlineDirectionArrows() {
        const startPoint = new THREE.Vector3(this.startPoint.x, this.startPoint.y, this.startPoint.z);
        const endPoint = new THREE.Vector3(this.endPoint.x, this.endPoint.y, this.endPoint.z);
        
        if (this.direction === PATH_DIRECTIONS.A_TO_B) {
            // A->B: 从A指向B的方向
            const direction = new THREE.Vector3().subVectors(endPoint, startPoint).normalize();
            const arrowPosition = new THREE.Vector3().addVectors(
                startPoint, 
                direction.clone().multiplyScalar(startPoint.distanceTo(endPoint) * 0.3),
            );
            const arrow = this.createInlineArrow(arrowPosition, direction, '>');
            arrow.userData.arrowType = 'inline_forward';
            this.group.add(arrow);
        } else if (this.direction === PATH_DIRECTIONS.B_TO_A) {
            // B->A: 从B指向A的方向
            const direction = new THREE.Vector3().subVectors(startPoint, endPoint).normalize();
            const arrowPosition = new THREE.Vector3().addVectors(
                endPoint, 
                direction.clone().multiplyScalar(startPoint.distanceTo(endPoint) * 0.3),
            );
            const arrow = this.createInlineArrow(arrowPosition, direction, '>');
            arrow.userData.arrowType = 'inline_backward';
            this.group.add(arrow);
        } else if (this.direction === PATH_DIRECTIONS.BIDIRECTIONAL) {
            // A<->B: 显示红色双向箭头
            const directionAB = new THREE.Vector3().subVectors(endPoint, startPoint).normalize();
            const pathLength = startPoint.distanceTo(endPoint);
            
            // 在路径中心位置创建双向箭头
            const arrowPosition = new THREE.Vector3().addVectors(
                startPoint, 
                directionAB.clone().multiplyScalar(pathLength * 0.5),
            );
            const bidirectionalArrow = this.createBidirectionalArrow(arrowPosition, directionAB);
            bidirectionalArrow.userData.arrowType = 'inline_bidirectional';
            
            this.group.add(bidirectionalArrow);
        }
    }

    // 创建线上箭头
    createInlineArrow(position, direction, arrowType) {
        const arrowGeometry = new THREE.BufferGeometry();
        const arrowSize = 0.15; // 稍小一些，不要太显眼
        
        let vertices;
        if (arrowType === '>') {
            // 右箭头
            vertices = new Float32Array([
                arrowSize, 0, 0,      // 箭头尖端
                -arrowSize / 2, arrowSize / 2, 0,   // 左上角
                -arrowSize / 2, -arrowSize / 2, 0,   // 左下角
            ]);
        } else if (arrowType === '<') {
            // 左箭头
            vertices = new Float32Array([
                -arrowSize, 0, 0,     // 箭头尖端
                arrowSize / 2, arrowSize / 2, 0,    // 右上角
                arrowSize / 2, -arrowSize / 2, 0,   // 右下角
            ]);
        }

        arrowGeometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3));
        arrowGeometry.computeVertexNormals();

        const arrowMaterial = new THREE.MeshBasicMaterial({
            color: 0x0066ff, // 蓝色，区别于端点箭头
            side: THREE.DoubleSide,
        });
        const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial);

        // 设置箭头位置
        arrow.position.copy(position);
        arrow.position.z = 0.12; // 稍微高一点避免与线条重叠

        // 设置箭头旋转
        const angle = Math.atan2(direction.y, direction.x);
        arrow.rotation.set(0, 0, angle);

        return arrow;
    }

    // 创建双向箭头
    createBidirectionalArrow(position, direction) {
        try {
            console.log('Creating bidirectional arrow at position:', position, 'direction:', direction);
            
            // 使用更简单的方法：创建两个单独的箭头
            const group = new THREE.Group();
            
            // 创建左箭头 <
            const leftArrowGeometry = new THREE.BufferGeometry();
            const arrowSize = 0.15;
            
            const leftVertices = new Float32Array([
                -arrowSize, 0, 0,                    // 箭头尖端
                arrowSize/2, arrowSize/2, 0,         // 右上角
                arrowSize/2, -arrowSize/2, 0,        // 右下角
            ]);
            
            leftArrowGeometry.setAttribute('position', new THREE.BufferAttribute(leftVertices, 3));
            leftArrowGeometry.computeVertexNormals();
            
            const leftArrowMaterial = new THREE.MeshBasicMaterial({
                color: 0x0066ff,
                side: THREE.DoubleSide,
            });
            const leftArrow = new THREE.Mesh(leftArrowGeometry, leftArrowMaterial);
            leftArrow.position.set(-arrowSize * 0.8, 0, 0);
            
            // 创建右箭头 >
            const rightArrowGeometry = new THREE.BufferGeometry();
            const rightVertices = new Float32Array([
                arrowSize, 0, 0,                     // 箭头尖端
                -arrowSize/2, arrowSize/2, 0,        // 左上角
                -arrowSize/2, -arrowSize/2, 0,       // 左下角
            ]);
            
            rightArrowGeometry.setAttribute('position', new THREE.BufferAttribute(rightVertices, 3));
            rightArrowGeometry.computeVertexNormals();
            
            const rightArrowMaterial = new THREE.MeshBasicMaterial({
                color: 0x0066ff,
                side: THREE.DoubleSide,
            });
            const rightArrow = new THREE.Mesh(rightArrowGeometry, rightArrowMaterial);
            rightArrow.position.set(arrowSize * 0.8, 0, 0);
            
            // 添加到组
            group.add(leftArrow);
            group.add(rightArrow);
            
            // 设置组的位置和旋转
            group.position.copy(position);
            group.position.z = 0.12;
            
            const angle = Math.atan2(direction.y, direction.x);
            group.rotation.set(0, 0, angle);
            
            console.log('Bidirectional arrow created successfully');
            return group;
            
        } catch (error) {
            console.error('Error creating bidirectional arrow:', error);
            // 返回一个简单的球体作为fallback
            const fallbackGeometry = new THREE.SphereGeometry(0.1, 8, 8);
            const fallbackMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
            const fallback = new THREE.Mesh(fallbackGeometry, fallbackMaterial);
            fallback.position.copy(position);
            fallback.position.z = 0.12;
            return fallback;
        }
    }

    // 清除现有箭头
    clearArrows() {
        const arrowsToRemove = [];
        this.group.traverse(child => {
            if (child.userData && child.userData.arrowType) {
                arrowsToRemove.push(child);
            }
        });
        arrowsToRemove.forEach(arrow => {
            this.group.remove(arrow);
            if (arrow.geometry) arrow.geometry.dispose();
            if (arrow.material) arrow.material.dispose();
        });
    }

    // 创建2D风格的箭头
    createArrow(position, fromPosition) {
        const direction = new THREE.Vector3()
            .subVectors(
                new THREE.Vector3(position.x, position.y, position.z),
                new THREE.Vector3(fromPosition.x, fromPosition.y, fromPosition.z),
            )
            .normalize();

        // 创建2D三角形箭头几何体
        const arrowGeometry = new THREE.BufferGeometry();
        const arrowSize = 0.2;

        // 定义三角形顶点（指向右侧的箭头）
        const vertices = new Float32Array([
            arrowSize, 0, 0,      // 箭头尖端
            -arrowSize / 2, arrowSize / 2, 0,   // 左上角
            -arrowSize / 2, -arrowSize / 2, 0,   // 左下角
        ]);

        arrowGeometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3));
        arrowGeometry.computeVertexNormals();

        const arrowMaterial = new THREE.MeshBasicMaterial({
            color: this.getLineColor(),
            side: THREE.DoubleSide, // 确保两面都可见
        });
        const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial);

        // 设置箭头位置，确保Z坐标严格限制在0.1
        arrow.position.set(position.x, position.y, 0.1);

        // 2D箭头只需要绕Z轴旋转
        const angle = Math.atan2(direction.y, direction.x);
        arrow.rotation.set(0, 0, angle);

        return arrow;
    }

    // 获取线条颜色
    getLineColor() {
        if (this.isSelected) return 0xff0000; // 选中时红色
        if (this.isEditing) return 0xffff00; // 编辑时黄色
        // return this.direction === PATH_DIRECTIONS.BIDIRECTIONAL ? 0x00ff00 : 0x0066ff
        return 0x00ff00; // 统一为绿色
    }

    // 获取线条宽度
    getLineWidth() {
        if (this.isSelected || this.isEditing) return 3;
        return 2;
    }

    // 更新外观
    updateAppearance() {
        if (this.line && this.line.material) {
            this.line.material.color.setHex(this.getLineColor());
        }
        // 重新创建路径以更新箭头颜色
        this.createPath();

        // 更新控制点可见性和颜色
        if (this.startControlPoint && this.endControlPoint) {
            const visible = this.isSelected || this.isEditing;
            this.startControlPoint.visible = visible;
            this.endControlPoint.visible = visible;

            // 选中状态时，控制点也变为红色
            if (this.isSelected) {
                this.startControlPoint.material.color.setHex(0xff0000); // 红色
                this.endControlPoint.material.color.setHex(0xff0000); // 红色
            } else {
                // 恢复原始颜色
                this.startControlPoint.material.color.setHex(0x00ff00); // 绿色起点
                this.endControlPoint.material.color.setHex(0xff0000); // 红色终点
            }
        }
    }

    // 更新控制点位置
    updateControlPoint(pointType, newPosition) {
        // 限制在XY平面，z=0.1避免与地板重叠
        const position = { x: newPosition.x, y: newPosition.y, z: 0.1 };

        if (pointType === 'startPoint') {
            this.startPoint = position;
            if (this.startControlPoint) {
                this.startControlPoint.position.set(position.x, position.y, 0.1);
            }
        } else if (pointType === 'endPoint') {
            this.endPoint = position;
            if (this.endControlPoint) {
                this.endControlPoint.position.set(position.x, position.y, 0.1);
            }
        }

        // 重新创建线条和方向指示器
        this.createPath();
    }

    // 获取路径数据（扩展基类）
    getPathData() {
        const baseData = super.getPathData();
        return {
            ...baseData,
            startPoint: { ...this.startPoint },
            endPoint: { ...this.endPoint },
            centralPoint: [], // 直线路径没有中间控制点，返回空数组
        };
    }

    // 更新属性（扩展基类）
    updateProperty(property, value) {
        super.updateProperty(property, value);

        // 处理特殊属性
        if (property === 'direction') {
            this.createPath(); // 重新创建以更新方向指示器
        } else if (property.startsWith('startPoint.') || property.startsWith('endPoint.')) {
            const [pointType, coord] = property.split('.');
            if (pointType === 'startPoint') {
                this.startPoint[coord] = coord === 'z' ? 0.1 : parseFloat(value) || 0;  // Z轴固定为0.1
                if (this.startControlPoint) {
                    this.startControlPoint.position[coord] = coord === 'z' ? 0.1 : this.startPoint[coord];
                }
            } else if (pointType === 'endPoint') {
                this.endPoint[coord] = coord === 'z' ? 0.1 : parseFloat(value) || 0;  // Z轴固定为0.1
                if (this.endControlPoint) {
                    this.endControlPoint.position[coord] = coord === 'z' ? 0.1 : this.endPoint[coord];
                }
            }
            this.createPath();
        }
    }

    // 计算路径长度
    getLength() {
        const dx = this.endPoint.x - this.startPoint.x;
        const dz = this.endPoint.z - this.startPoint.z;
        return Math.sqrt(dx * dx + dz * dz);
    }
}
