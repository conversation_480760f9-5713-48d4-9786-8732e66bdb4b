/**
 * 贝塞尔曲线路径类
 */
import * as THREE from 'three';
import { BasePath, PATH_DIRECTIONS } from './pathSystem.js';

export class BezierPath extends BasePath {
    constructor(name, mapId, startPoint = { x: 0, y: 0, z: 0 }, endPoint = { x: 3, y: 0, z: 0 }) {
        super('bezier', name, mapId);

        // 路径点（限制在XY平面，z=0.1避免与地板重叠）
        this.startPoint = { x: startPoint.x, y: startPoint.y, z: 0.1 };
        this.endPoint = { x: endPoint.x, y: endPoint.y, z: 0.1 };

        // 控制点（默认在中间位置稍微偏移）
        const midX = (startPoint.x + endPoint.x) / 2;
        const midY = (startPoint.y + endPoint.y) / 2;
        this.bezierControlPoints = [
            { x: midX, y: midY + 1, z: 0.1 }, // 默认一个控制点
        ];

        // 3D控制点对象
        this.startControlPoint = null;
        this.endControlPoint = null;
        this.bezierControlMeshes = [];

        // 曲线对象
        this.curve = null;

        this.createPath();
    }

    // 创建路径
    createPath() {
        this.group.clear();
        this.bezierControlMeshes = [];

        // 创建曲线
        this.createCurve();

        // 创建控制点
        this.createControlPoints();

        // 创建方向指示器
        this.createDirectionIndicator();
    }

    // 创建曲线
    createCurve() {
        // 构建曲线点数组
        const curvePoints = [
            new THREE.Vector3(this.startPoint.x, this.startPoint.y, this.startPoint.z),
        ];

        // 添加贝塞尔控制点
        this.bezierControlPoints.forEach(point => {
            curvePoints.push(new THREE.Vector3(point.x, point.y, point.z));
        });

        // 添加终点
        curvePoints.push(new THREE.Vector3(this.endPoint.x, this.endPoint.y, this.endPoint.z));

        // 创建Catmull-Rom曲线（平滑插值）
        this.curve = new THREE.CatmullRomCurve3(curvePoints, false, 'centripetal', 0.5);

        // 不调整曲线长度，让箭头直接贴在曲线端点上
        // 这样可以避免箭头与曲线分离的问题
        let adjustedCurve = this.curve;

        // 创建可视的2D风格管道几何体 - 使用更扁平的管道
        const visualTubeRadius = 0.015; // 可视半径
        const radialSegments = 6; // 减少径向分段数，使其看起来更2D
        const visualTubeGeometry = new THREE.TubeGeometry(adjustedCurve, 100, visualTubeRadius, radialSegments, false);

        const material = new THREE.MeshBasicMaterial({
            color: this.getLineColor(),
        });

        this.line = new THREE.Mesh(visualTubeGeometry, material);

        // 确保路径严格限制在XY平面上
        this.line.position.z = 0;
        this.line.userData = {
            objectType: 'pathLine',
            pathUuid: this.uuid,
            clickable: true, // 可点击添加控制点
        };

        // 创建不可见的碰撞检测几何体 - 更粗的管道用于点击检测
        const collisionTubeRadius = visualTubeRadius * 4; // 碰撞检测半径是可视半径的4倍
        const collisionTubeGeometry = new THREE.TubeGeometry(adjustedCurve, 100, collisionTubeRadius, 8, false);

        // 不可见材质
        const collisionMaterial = new THREE.MeshBasicMaterial({
            transparent: true,
            opacity: 0,
            visible: false, // 完全不可见
        });

        this.collisionMesh = new THREE.Mesh(collisionTubeGeometry, collisionMaterial);
        this.collisionMesh.position.z = 0;
        this.collisionMesh.userData = {
            objectType: 'pathLineCollision',
            pathUuid: this.uuid,
            clickable: true,
            isCollisionMesh: true, // 标记为碰撞检测网格
        };

        this.group.add(this.line);
        this.group.add(this.collisionMesh); // 添加碰撞检测网格
    }

    // 创建修剪后的曲线
    createTrimmedCurve(originalCurve, startT, endT) {
        // 获取修剪后的点
        const trimmedPoints = [];
        const segments = 100;

        for (let i = 0; i <= segments; i++) {
            const t = startT + (endT - startT) * (i / segments);
            const point = originalCurve.getPoint(Math.max(0, Math.min(1, t)));
            trimmedPoints.push(point);
        }

        return new THREE.CatmullRomCurve3(trimmedPoints, false, 'centripetal', 0.5);
    }

    // 创建控制点
    createControlPoints() {
        // 起点控制点
        this.startControlPoint = this.createControlPoint(
            this.startPoint,
            0x00ff00, // 绿色
            'startPoint',
        );

        // 终点控制点
        this.endControlPoint = this.createControlPoint(
            this.endPoint,
            0xff0000, // 红色
            'endPoint',
        );

        this.group.add(this.startControlPoint);
        this.group.add(this.endControlPoint);

        // 贝塞尔控制点
        this.bezierControlPoints.forEach((point, index) => {
            const controlMesh = this.createControlPoint(
                point,
                0xffff00, // 黄色
                'bezierControl',
                index,
            );
            this.bezierControlMeshes.push(controlMesh);
            this.group.add(controlMesh);
        });
    }

    // 创建单个控制点
    createControlPoint(position, color, pointType, index = -1) {
        // 根据地图复杂度动态调整控制点大小，提高可选择性
        const baseRadius = 0.2;
        const radius = baseRadius * (this.mapComplexity > 1000000 ? 1.5 : 1.0); // 大地图使用更大的控制点

        const geometry = new THREE.SphereGeometry(radius, 16, 16);
        const material = new THREE.MeshBasicMaterial({
            color: color,
            transparent: true,
            opacity: 0.8, // 半透明以减少视觉干扰
        });
        const sphere = new THREE.Mesh(geometry, material);

        // 确保控制点严格限制在XY平面上
        sphere.position.set(position.x, position.y, 0.1);
        sphere.userData = {
            draggable: true,
            objectType: 'pathControlPoint',
            pathUuid: this.uuid,
            pointType: pointType,
            pointIndex: index,
        };

        return sphere;
    }

    // 创建方向指示器
    createDirectionIndicator() {
        // 清除现有的箭头
        this.clearArrows();

        if (this.curve) {
            // 使用原始曲线的点来确定箭头位置和方向
            const points = this.curve.getPoints(50);

            // 如果有绑定的位置A和位置B，显示端点箭头和线上箭头
            if (this.positionA && this.positionB) {
                // 端点箭头（保留原有逻辑）
                if (this.direction === PATH_DIRECTIONS.A_TO_B) {
                    const endPoint = points[points.length - 1];
                    const beforeEndPoint = points[points.length - 5] || points[points.length - 2];
                    const arrow = this.createArrow(endPoint, beforeEndPoint);
                    arrow.userData.arrowType = 'end';
                    this.group.add(arrow);
                } else if (this.direction === PATH_DIRECTIONS.B_TO_A) {
                    const startPoint = points[0];
                    const afterStartPoint = points[4] || points[1];
                    const arrow = this.createArrow(startPoint, afterStartPoint);
                    arrow.userData.arrowType = 'start';
                    this.group.add(arrow);
                } else if (this.direction === PATH_DIRECTIONS.BIDIRECTIONAL) {
                    const startPoint = points[0];
                    const afterStartPoint = points[4] || points[1];
                    const endPoint = points[points.length - 1];
                    const beforeEndPoint = points[points.length - 5] || points[points.length - 2];

                    const arrow1 = this.createArrow(endPoint, beforeEndPoint);
                    const arrow2 = this.createArrow(startPoint, afterStartPoint);
                    arrow1.userData.arrowType = 'end';
                    arrow2.userData.arrowType = 'start';
                    this.group.add(arrow1);
                    this.group.add(arrow2);
                }

                // 线上方向箭头（新增）
                this.createInlineDirectionArrows(points);
            } else {
                // 如果没有绑定位置，只显示端点箭头（原有逻辑）
                if (this.direction === PATH_DIRECTIONS.A_TO_B) {
                    const endPoint = points[points.length - 1];
                    const beforeEndPoint = points[points.length - 5] || points[points.length - 2];
                    const arrow = this.createArrow(endPoint, beforeEndPoint);
                    arrow.userData.arrowType = 'end';
                    this.group.add(arrow);
                } else if (this.direction === PATH_DIRECTIONS.B_TO_A) {
                    const startPoint = points[0];
                    const afterStartPoint = points[4] || points[1];
                    const arrow = this.createArrow(startPoint, afterStartPoint);
                    arrow.userData.arrowType = 'start';
                    this.group.add(arrow);
                } else if (this.direction === PATH_DIRECTIONS.BIDIRECTIONAL) {
                    const startPoint = points[0];
                    const afterStartPoint = points[4] || points[1];
                    const endPoint = points[points.length - 1];
                    const beforeEndPoint = points[points.length - 5] || points[points.length - 2];

                    const arrow1 = this.createArrow(endPoint, beforeEndPoint);
                    const arrow2 = this.createArrow(startPoint, afterStartPoint);
                    arrow1.userData.arrowType = 'end';
                    arrow2.userData.arrowType = 'start';
                    this.group.add(arrow1);
                    this.group.add(arrow2);
                }
            }
        }
    }

    // 创建线上方向箭头
    createInlineDirectionArrows(points) {
        if (!points || points.length < 2) return;
        
        if (this.direction === PATH_DIRECTIONS.A_TO_B) {
            // A->B: 从A指向B的方向
            const arrowIndex = Math.floor(points.length * 0.3);
            const arrowPoint = points[arrowIndex];
            const nextPoint = points[arrowIndex + 1] || points[arrowIndex];
            const direction = new THREE.Vector3().subVectors(nextPoint, arrowPoint).normalize();
            
            const arrow = this.createInlineArrow(arrowPoint, direction, '>');
            arrow.userData.arrowType = 'inline_forward';
            this.group.add(arrow);
        } else if (this.direction === PATH_DIRECTIONS.B_TO_A) {
            // B->A: 从B指向A的方向，在曲线70%位置处
            const arrowIndex = Math.floor(points.length * 0.7);
            const arrowPoint = points[arrowIndex];
            const prevPoint = points[arrowIndex - 1] || points[arrowIndex];
            const direction = new THREE.Vector3().subVectors(prevPoint, arrowPoint).normalize();
            
            const arrow = this.createInlineArrow(arrowPoint, direction, '>');
            arrow.userData.arrowType = 'inline_backward';
            this.group.add(arrow);
        } else if (this.direction === PATH_DIRECTIONS.BIDIRECTIONAL) {
            // A<->B: 显示红色双向箭头，智能避开控制点
            if (this.curve) {
                // 尝试多个位置找到最佳箭头位置
                const candidatePositions = [0.4, 0.5, 0.6, 0.3, 0.7]; // 多个候选位置
                let bestPosition = 0.5;
                let maxDistance = 0;
                
                for (const t of candidatePositions) {
                    const testPoint = this.curve.getPoint(t);
                    let minDistanceToControl = Infinity;
                    
                    // 检查与所有控制点的距离
                    if (this.bezierControlPoints && this.bezierControlPoints.length > 0) {
                        for (const controlPoint of this.bezierControlPoints) {
                            const distance = testPoint.distanceTo(controlPoint);
                            minDistanceToControl = Math.min(minDistanceToControl, distance);
                        }
                    }
                    
                    // 选择距离控制点最远的位置
                    if (minDistanceToControl > maxDistance) {
                        maxDistance = minDistanceToControl;
                        bestPosition = t;
                    }
                }
                
                const arrowPoint = this.curve.getPoint(bestPosition);
                const tangent = this.curve.getTangent(bestPosition).normalize();
                
                const bidirectionalArrow = this.createBidirectionalArrow(arrowPoint, tangent);
                bidirectionalArrow.userData.arrowType = 'inline_bidirectional';
                
                this.group.add(bidirectionalArrow);
            } else {
                // 如果没有曲线，使用原来的方法
                const arrowIndex = Math.floor(points.length * 0.5);
                const arrowPoint = points[arrowIndex];
                const nextPoint = points[arrowIndex + 1] || points[arrowIndex];
                const direction = new THREE.Vector3().subVectors(nextPoint, arrowPoint).normalize();
                
                const bidirectionalArrow = this.createBidirectionalArrow(arrowPoint, direction);
                bidirectionalArrow.userData.arrowType = 'inline_bidirectional';
                
                this.group.add(bidirectionalArrow);
            }
        }
    }

    // 创建线上箭头
    createInlineArrow(position, direction, arrowType) {
        const arrowGeometry = new THREE.BufferGeometry();
        const arrowSize = 0.15; // 稍小一些，不要太显眼
        
        let vertices;
        if (arrowType === '>') {
            // 右箭头
            vertices = new Float32Array([
                arrowSize, 0, 0,      // 箭头尖端
                -arrowSize / 2, arrowSize / 2, 0,   // 左上角
                -arrowSize / 2, -arrowSize / 2, 0,   // 左下角
            ]);
        } else if (arrowType === '<') {
            // 左箭头
            vertices = new Float32Array([
                -arrowSize, 0, 0,     // 箭头尖端
                arrowSize / 2, arrowSize / 2, 0,    // 右上角
                arrowSize / 2, -arrowSize / 2, 0,   // 右下角
            ]);
        }

        arrowGeometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3));
        arrowGeometry.computeVertexNormals();

        const arrowMaterial = new THREE.MeshBasicMaterial({
            color: 0x0066ff, // 蓝色，区别于端点箭头
            side: THREE.DoubleSide,
        });
        const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial);

        // 设置箭头位置
        arrow.position.copy(position);
        arrow.position.z = 0.12; // 稍微高一点避免与线条重叠

        // 设置箭头旋转
        const angle = Math.atan2(direction.y, direction.x);
        arrow.rotation.set(0, 0, angle);

        return arrow;
    }

    // 清除现有箭头
    clearArrows() {
        const arrowsToRemove = [];
        this.group.traverse(child => {
            if (child.userData && child.userData.arrowType) {
                arrowsToRemove.push(child);
            }
        });
        arrowsToRemove.forEach(arrow => {
            this.group.remove(arrow);
            if (arrow.geometry) arrow.geometry.dispose();
            if (arrow.material) arrow.material.dispose();
        });
    }

    // 创建2D风格的箭头
    createArrow(position, fromPosition) {
        const direction = new THREE.Vector3()
            .subVectors(position, fromPosition)
            .normalize();

        // 创建2D三角形箭头几何体
        const arrowGeometry = new THREE.BufferGeometry();
        const arrowSize = 0.2;

        // 定义三角形顶点（指向右侧的箭头）
        const vertices = new Float32Array([
            arrowSize, 0, 0,      // 箭头尖端
            -arrowSize / 2, arrowSize / 2, 0,   // 左上角
            -arrowSize / 2, -arrowSize / 2, 0,   // 左下角
        ]);

        arrowGeometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3));
        arrowGeometry.computeVertexNormals();

        const arrowMaterial = new THREE.MeshBasicMaterial({
            color: this.getLineColor(),
            side: THREE.DoubleSide, // 确保两面都可见
        });
        const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial);

        // 设置箭头位置，确保Z坐标严格限制在0.1
        arrow.position.set(position.x, position.y, 0.1);

        // 2D箭头只需要绕Z轴旋转
        const angle = Math.atan2(direction.y, direction.x);
        arrow.rotation.set(0, 0, angle);

        return arrow;
    }

    // 创建双向箭头
    createBidirectionalArrow(position, direction) {
        try {
            console.log('Creating bidirectional arrow at position:', position, 'direction:', direction);
            
            // 使用更简单的方法：创建两个单独的箭头
            const group = new THREE.Group();
            
            // 创建左箭头 <
            const leftArrowGeometry = new THREE.BufferGeometry();
            const arrowSize = 0.15;
            
            const leftVertices = new Float32Array([
                -arrowSize, 0, 0,                    // 箭头尖端
                arrowSize/2, arrowSize/2, 0,         // 右上角
                arrowSize/2, -arrowSize/2, 0,        // 右下角
            ]);
            
            leftArrowGeometry.setAttribute('position', new THREE.BufferAttribute(leftVertices, 3));
            leftArrowGeometry.computeVertexNormals();
            
            const leftArrowMaterial = new THREE.MeshBasicMaterial({
                color: 0x0066ff,
                side: THREE.DoubleSide,
            });
            const leftArrow = new THREE.Mesh(leftArrowGeometry, leftArrowMaterial);
            leftArrow.position.set(-arrowSize * 0.8, 0, 0);
            
            // 创建右箭头 >
            const rightArrowGeometry = new THREE.BufferGeometry();
            const rightVertices = new Float32Array([
                arrowSize, 0, 0,                     // 箭头尖端
                -arrowSize/2, arrowSize/2, 0,        // 左上角
                -arrowSize/2, -arrowSize/2, 0,       // 左下角
            ]);
            
            rightArrowGeometry.setAttribute('position', new THREE.BufferAttribute(rightVertices, 3));
            rightArrowGeometry.computeVertexNormals();
            
            const rightArrowMaterial = new THREE.MeshBasicMaterial({
                color: 0x0066ff,
                side: THREE.DoubleSide,
            });
            const rightArrow = new THREE.Mesh(rightArrowGeometry, rightArrowMaterial);
            rightArrow.position.set(arrowSize * 0.8, 0, 0);
            
            // 添加到组
            group.add(leftArrow);
            group.add(rightArrow);
            
            // 设置组的位置和旋转
            group.position.copy(position);
            group.position.z = 0.12;
            
            const angle = Math.atan2(direction.y, direction.x);
            group.rotation.set(0, 0, angle);
            
            console.log('Bidirectional arrow created successfully');
            return group;
            
        } catch (error) {
            console.error('Error creating bidirectional arrow:', error);
            // 返回一个简单的球体作为fallback
            const fallbackGeometry = new THREE.SphereGeometry(0.1, 8, 8);
            const fallbackMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
            const fallback = new THREE.Mesh(fallbackGeometry, fallbackMaterial);
            fallback.position.copy(position);
            fallback.position.z = 0.12;
            return fallback;
        }
    }

    // 获取线条颜色
    getLineColor() {
        if (this.isSelected) return 0xff0000; // 选中时红色
        if (this.isEditing) return 0xffff00; // 编辑时黄色
        // return this.direction === PATH_DIRECTIONS.BIDIRECTIONAL ? 0x00ff00 : 0x9966ff // 紫色
        return 0x00ff00; // 统一为绿色
    }

    // 获取线条宽度
    getLineWidth() {
        if (this.isSelected || this.isEditing) return 3;
        return 2;
    }

    // 更新外观
    updateAppearance() {
        if (this.line && this.line.material) {
            this.line.material.color.setHex(this.getLineColor());
        }
        // 重新创建路径以更新箭头颜色
        this.createPath();

        // 更新控制点可见性和颜色
        const visible = this.isSelected || this.isEditing;
        if (this.startControlPoint) {
            this.startControlPoint.visible = visible;
            // 选中状态时，控制点也变为红色
            if (this.isSelected) {
                this.startControlPoint.material.color.setHex(0xff0000); // 红色
            } else {
                this.startControlPoint.material.color.setHex(0x00ff00); // 绿色起点
            }
        }

        if (this.endControlPoint) {
            this.endControlPoint.visible = visible;
            // 选中状态时，控制点也变为红色
            if (this.isSelected) {
                this.endControlPoint.material.color.setHex(0xff0000); // 红色
            } else {
                this.endControlPoint.material.color.setHex(0xff0000); // 红色终点
            }
        }

        this.bezierControlMeshes.forEach(mesh => {
            mesh.visible = visible;
            // 选中状态时，贝塞尔控制点也变为红色
            if (this.isSelected) {
                mesh.material.color.setHex(0xff0000); // 红色
            } else {
                mesh.material.color.setHex(0xffff00); // 黄色贝塞尔控制点
            }
        });
    }

    // 更新控制点位置
    updateControlPoint(pointType, newPosition, pointIndex = -1) {
        // 限制在XY平面，z=0.1避免与地板重叠
        const position = { x: newPosition.x, y: newPosition.y, z: 0.1 };

        if (pointType === 'startPoint') {
            this.startPoint = position;
            if (this.startControlPoint) {
                this.startControlPoint.position.set(position.x, position.y, 0.1);
            }
        } else if (pointType === 'endPoint') {
            this.endPoint = position;
            if (this.endControlPoint) {
                this.endControlPoint.position.set(position.x, position.y, 0.1);
            }
        } else if (pointType === 'bezierControl' && pointIndex >= 0 && pointIndex < this.bezierControlPoints.length) {
            this.bezierControlPoints[pointIndex] = position;
            if (this.bezierControlMeshes[pointIndex]) {
                this.bezierControlMeshes[pointIndex].position.set(position.x, position.y, 0.1);
            }
        }

        // 重新创建曲线
        this.createPath();
    }

    // 添加控制点
    addControlPoint(position) {
        // 限制在XY平面，z=0.1避免与地板重叠
        const clickPoint = { x: position.x, y: position.y, z: 0.1 };

        // 找到最佳插入位置，但使用双击的确切位置作为新控制点
        const insertIndex = this.findBestInsertIndex(clickPoint);

        // 直接使用双击位置作为新控制点，确保位置精确
        const newControlPoint = {
            x: clickPoint.x,
            y: clickPoint.y,
            z: 0.1,
        };

        this.bezierControlPoints.splice(insertIndex, 0, newControlPoint);
        this.createPath();
    }

    // 找到插入新控制点的最佳索引位置
    findBestInsertIndex(clickPoint) {
        if (this.bezierControlPoints.length === 0) {
            return 0;
        }

        // 如果当前曲线存在，使用曲线上的点来找最佳插入位置
        if (this.curve) {
            let minDistance = Infinity;
            let bestT = 0;

            // 在曲线上采样多个点，找到最接近双击位置的点的t值
            const sampleCount = 200;
            for (let i = 0; i <= sampleCount; i++) {
                const t = i / sampleCount;
                const pointOnCurve = this.curve.getPoint(t);

                const distance = Math.sqrt(
                    Math.pow(pointOnCurve.x - clickPoint.x, 2) +
                    Math.pow(pointOnCurve.z - clickPoint.z, 2),
                );

                if (distance < minDistance) {
                    minDistance = distance;
                    bestT = t;
                }
            }

            // 根据t值确定插入位置
            // t值表示在曲线上的相对位置，我们根据这个来确定在控制点数组中的插入位置
            let insertIndex;

            if (this.bezierControlPoints.length === 1) {
                // 只有一个控制点的情况
                if (bestT < 0.5) {
                    insertIndex = 0; // 插入到现有控制点之前
                } else {
                    insertIndex = 1; // 插入到现有控制点之后
                }
            } else {
                // 多个控制点的情况，根据t值比例确定插入位置
                // t值越大，插入位置越靠后
                insertIndex = Math.round(bestT * this.bezierControlPoints.length);
                insertIndex = Math.max(0, Math.min(this.bezierControlPoints.length, insertIndex));
            }

            return insertIndex;
        }

        // 如果没有曲线，返回中间位置
        return Math.floor(this.bezierControlPoints.length / 2);
    }



    // 删除控制点
    removeControlPoint(index) {
        if (index >= 0 && index < this.bezierControlPoints.length) {
            this.bezierControlPoints.splice(index, 1);
            this.createPath();
        }
    }

    // 获取路径数据（扩展基类）
    getPathData() {
        const baseData = super.getPathData();

        // 按照方向顺序获取中间控制点坐标
        const centralPoint = this.getCentralPointsByDirection();

        return {
            ...baseData,
            startPoint: { ...this.startPoint },
            endPoint: { ...this.endPoint },
            controlPoints: this.bezierControlPoints.map(p => ({ ...p })), // 保持原有字段兼容性
            centralPoint: centralPoint, // 新增按方向排序的中间控制点
        };
    }

    // 根据方向获取中间控制点坐标
    getCentralPointsByDirection() {
        if (!this.bezierControlPoints || this.bezierControlPoints.length === 0) {
            return [];
        }

        // 复制控制点数组，避免修改原数组
        let orderedPoints = this.bezierControlPoints.map(p => ({
            x: Math.round((parseFloat(p.x) || 0) * 100) / 100,
            y: Math.round((parseFloat(p.y) || 0) * 100) / 100,
            z: Math.round((parseFloat(p.z) || 0) * 100) / 100,
        }));

        // 根据方向决定控制点顺序
        if (this.direction === 'B_to_A') {
            // B->A方向，需要反转控制点顺序
            orderedPoints = orderedPoints.reverse();
        }
        // A->B 和 A<->B 都使用正常顺序（A<->B按A->B处理）

        console.log(`获取贝塞尔曲线 ${this.name} 的中间控制点，方向: ${this.direction}，控制点数量: ${orderedPoints.length}`);

        return orderedPoints;
    }

    // 更新属性（扩展基类）
    updateProperty(property, value) {
        super.updateProperty(property, value);

        // 处理特殊属性
        if (property === 'direction') {
            this.createPath(); // 重新创建以更新方向指示器
        } else if (property.startsWith('startPoint.') || property.startsWith('endPoint.')) {
            const [pointType, coord] = property.split('.');
            if (pointType === 'startPoint') {
                this.startPoint[coord] = coord === 'z' ? 0.1 : parseFloat(value) || 0;  // Z轴固定为0.1
                if (this.startControlPoint) {
                    this.startControlPoint.position[coord] = coord === 'z' ? 0.1 : this.startPoint[coord];
                }
            } else if (pointType === 'endPoint') {
                this.endPoint[coord] = coord === 'z' ? 0.1 : parseFloat(value) || 0;  // Z轴固定为0.1
                if (this.endControlPoint) {
                    this.endControlPoint.position[coord] = coord === 'z' ? 0.1 : this.endPoint[coord];
                }
            }
            this.createPath();
        }
    }

    // 计算路径长度
    getLength() {
        if (this.curve) {
            return this.curve.getLength();
        }
        return 0;
    }
}
