/**
 * 渲染优化工具
 * 根据性能指标自动调整渲染设置
 */

export class RenderOptimizer {
    constructor() {
        this.isEnabled = true;
        this.optimizationLevel = 'auto'; // auto, low, medium, high
        this.lastOptimization = 0;
        this.optimizationInterval = 2000; // 2秒检查一次

        this.settings = {
            auto: {
                maxPoints: 10000000,
                pointSize: 0.05,
                lodDistances: [50, 100, 200, 500],
                textureMaxSize: 2048,
            },
            low: {
                maxPoints: 5000000,
                pointSize: 0.08,
                lodDistances: [30, 60, 120, 300],
                textureMaxSize: 1024,
            },
            medium: {
                maxPoints: 10000000,
                pointSize: 0.05,
                lodDistances: [50, 100, 200, 500],
                textureMaxSize: 2048,
            },
            high: {
                maxPoints: 20000000,
                pointSize: 0.03,
                lodDistances: [100, 200, 400, 800],
                textureMaxSize: 4096,
            },
        };
    }

    /**
     * 根据性能统计自动优化
     */
    autoOptimize(performanceStats, pgmRenderer, pcdRenderer) {
        if (!this.isEnabled) return;

        const now = Date.now();
        if (now - this.lastOptimization < this.optimizationInterval) return;

        this.lastOptimization = now;

        const { fps, memoryMB } = performanceStats;
        let newLevel = this.optimizationLevel;

        // 根据FPS调整优化级别
        if (fps < 20) {
            newLevel = 'low';
        } else if (fps < 30) {
            newLevel = 'medium';
        } else if (fps > 50 && memoryMB < 200) {
            newLevel = 'high';
        } else {
            newLevel = 'medium';
        }

        // 如果优化级别发生变化，应用新设置
        if (newLevel !== this.optimizationLevel) {
            this.applyOptimization(newLevel, pgmRenderer, pcdRenderer);
        }

        // 特殊情况处理
        this.handleSpecialCases(performanceStats, pgmRenderer, pcdRenderer);
    }

    /**
     * 应用优化设置
     */
    applyOptimization(level, pgmRenderer, pcdRenderer) {
        const settings = this.settings[level];
        if (!settings) return;

        console.log(`应用渲染优化级别: ${level}`);

        // 优化PCD渲染器
        if (pcdRenderer) {
            pcdRenderer.setMaxPoints(settings.maxPoints);
            pcdRenderer.setLODDistances(settings.lodDistances);

            // 更新点云材质
            if (pcdRenderer.pcdMesh && pcdRenderer.pcdMesh.material) {
                pcdRenderer.updatePointCloudMaterial({
                    pointSize: settings.pointSize,
                });
            }
        }

        // 优化PGM渲染器
        if (pgmRenderer) {
            // PGM渲染器的优化主要在纹理缓存方面
            // 可以在这里添加纹理质量调整
        }

        this.optimizationLevel = level;
        console.log(`渲染优化已应用: ${level}`, settings);
    }

    /**
     * 处理特殊情况
     */
    handleSpecialCases(performanceStats, pgmRenderer, pcdRenderer) {
        const { fps, memoryMB, points } = performanceStats;

        // 内存过高时清理缓存
        if (memoryMB > 400) {
            console.warn('内存使用过高，清理缓存');
            if (pgmRenderer) {
                pgmRenderer.clearCache();
            }
            if (pcdRenderer) {
                pcdRenderer.clearCache();
            }
        }

        // FPS极低时强制降级
        if (fps < 15 && pcdRenderer && pcdRenderer.hasPCD()) {
            console.warn('FPS极低，强制切换到最低LOD');
            const lodLevels = pcdRenderer.lodLevels.length;
            if (lodLevels > 0) {
                pcdRenderer.switchToLOD(lodLevels - 1); // 切换到最低LOD
            }
        }

        // 点数过多时自动减少
        if (points > 300000 && pcdRenderer) {
            console.warn('渲染点数过多，自动减少');
            pcdRenderer.setMaxPoints(Math.min(5000000, pcdRenderer.maxPoints));
        }
    }

    /**
     * 手动设置优化级别
     */
    setOptimizationLevel(level, pgmRenderer, pcdRenderer) {
        if (!this.settings[level]) {
            console.error('无效的优化级别:', level);
            return;
        }

        this.applyOptimization(level, pgmRenderer, pcdRenderer);
    }

    /**
     * 启用/禁用自动优化
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        console.log(`渲染优化${enabled ? '已启用' : '已禁用'}`);
    }

    /**
     * 设置优化间隔
     */
    setOptimizationInterval(interval) {
        this.optimizationInterval = interval;
        console.log(`优化检查间隔设置为: ${interval}ms`);
    }

    /**
     * 获取当前设置
     */
    getCurrentSettings() {
        return {
            level: this.optimizationLevel,
            enabled: this.isEnabled,
            interval: this.optimizationInterval,
            settings: this.settings[this.optimizationLevel],
        };
    }

    /**
     * 获取优化建议
     */
    getOptimizationAdvice(performanceStats) {
        const { fps, memoryMB, points } = performanceStats;
        const advice = [];

        if (fps < 30) {
            advice.push({
                type: 'fps',
                severity: fps < 20 ? 'high' : 'medium',
                message: `FPS较低(${fps})，建议减少点云密度或降低渲染质量`,
                action: 'reduce_quality',
            });
        }

        if (memoryMB > 300) {
            advice.push({
                type: 'memory',
                severity: memoryMB > 500 ? 'high' : 'medium',
                message: `内存使用较高(${memoryMB}MB)，建议清理缓存`,
                action: 'clear_cache',
            });
        }

        if (points > 150000) {
            advice.push({
                type: 'points',
                severity: points > 250000 ? 'high' : 'medium',
                message: `渲染点数较多(${points.toLocaleString()})，建议启用LOD`,
                action: 'enable_lod',
            });
        }

        return advice;
    }

    /**
     * 预设配置
     */
    applyPreset(preset, pgmRenderer, pcdRenderer) {
        const presets = {
            performance: {
                level: 'low',
                description: '性能优先，适合低端设备',
            },
            balanced: {
                level: 'medium',
                description: '平衡模式，适合大多数设备',
            },
            quality: {
                level: 'high',
                description: '质量优先，适合高端设备',
            },
        };

        const config = presets[preset];
        if (config) {
            this.setOptimizationLevel(config.level, pgmRenderer, pcdRenderer);
            console.log(`已应用预设: ${preset} - ${config.description}`);
        }
    }

    /**
     * 重置为默认设置
     */
    reset(pgmRenderer, pcdRenderer) {
        this.optimizationLevel = 'auto';
        this.isEnabled = true;
        this.lastOptimization = 0;
        this.applyOptimization('medium', pgmRenderer, pcdRenderer);
        console.log('渲染优化已重置为默认设置');
    }
}

// 创建全局渲染优化器实例
export const renderOptimizer = new RenderOptimizer();
