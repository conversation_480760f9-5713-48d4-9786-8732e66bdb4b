import { notification } from 'ant-design-vue';

/**
 * 请求错误
 * @param {} err 
 */
export function requestFailed(err) {
    console.error(err);
    notification['error']({
        message: '错误',
        description: ((err.response || {}).data || {}).message || '请求出现错误，请稍后再试',
        duration: 8,
    });
}

/**
 * 请求正常
 * @param {} response 
 */
export function requestSuccess(response) {
    if (response.errorCode !== 0) {
        notification['error']({
            message: '错误',
            description: response.errorMessage,
            duration: 8,
        });
    }
}

/**
 * 错误提示
 * @param {} response 
 */
export function notificationError(message, title) {
    notification['error']({
        message: title || '操作失败',
        description: message,
        duration: 8,
    });
}


/**
 * 错误提示
 * @param {} response 
 */
export function notificationSuccess(message, title) {
    notification['success']({
        message: title || '操作成功',
        description: message,
        duration: 8,
    });
}