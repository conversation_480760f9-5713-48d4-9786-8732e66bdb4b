<template>
    <div id="canvas-container" style="width: 100%; height: 100vh;"></div>
</template>

<script setup>
import * as THREE from 'three';
import { onMounted, onUnmounted } from 'vue';

let animationId = null;

onMounted(() => {
    console.log("hello world", THREE.Scene)
    init()
})

onUnmounted(() => {
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
})

const init = () => {
    console.log("init")
    const scene = new THREE.Scene();
    const geometry = new THREE.BoxGeometry(20, 20, 20)

    const material = new THREE.MeshBasicMaterial({ color: 'pink', transparent: true, opacity: 0.6 })

    const mesh = new THREE.Mesh(geometry, material)

    mesh.position.set(20, 25, 0)

    scene.add(mesh)

    const camera = new THREE.PerspectiveCamera(100, window.innerWidth / window.innerHeight, 0.1, 2000)

    camera.position.z = 100

    camera.lookAt(mesh.position)

    const renderer = new THREE.WebGLRenderer({ antialias: true })

    // 设置渲染器尺寸
    const container = document.getElementById('canvas-container')

    renderer.setSize(container.clientWidth, container.clientHeight)
    renderer.setPixelRatio(window.devicePixelRatio)

    container.appendChild(renderer.domElement)

    const axesHelper = new THREE.AxesHelper(50)
    scene.add(axesHelper)

    // 添加渲染循环
    const animate = () => {
        animationId = requestAnimationFrame(animate)

        // 添加简单的旋转动画
        // mesh.rotation.x += 0.01
        // mesh.rotation.y += 0.01

        renderer.render(scene, camera)
    }

    animate()
}

</script>

<style scoped></style>