/**
 * 性能监控工具
 * 用于监控3D渲染性能和资源使用情况
 */

export class PerformanceMonitor {
    constructor() {
        this.isMonitoring = false;
        this.stats = {
            fps: 0,
            frameTime: 0,
            memoryUsage: 0,
            renderCalls: 0,
            triangles: 0,
            points: 0,
        };

        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fpsHistory = [];
        this.maxHistoryLength = 60; // 保存60帧的历史数据

        this.callbacks = [];
        this.thresholds = {
            lowFPS: 30,
            highMemory: 500 * 1024 * 1024, // 500MB
            maxPoints: 200000,
        };
    }

    /**
     * 开始监控
     */
    start() {
        this.isMonitoring = true;
        this.lastTime = performance.now();
        this.frameCount = 0;
        console.log('性能监控已启动');
    }

    /**
     * 停止监控
     */
    stop() {
        this.isMonitoring = false;
        console.log('性能监控已停止');
    }

    /**
     * 更新性能统计
     */
    update(renderer) {
        if (!this.isMonitoring) return;

        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastTime;

        this.frameCount++;

        // 每秒更新一次FPS
        if (deltaTime >= 1000) {
            this.stats.fps = Math.round((this.frameCount * 1000) / deltaTime);
            this.stats.frameTime = deltaTime / this.frameCount;

            // 更新FPS历史
            this.fpsHistory.push(this.stats.fps);
            if (this.fpsHistory.length > this.maxHistoryLength) {
                this.fpsHistory.shift();
            }

            // 重置计数器
            this.frameCount = 0;
            this.lastTime = currentTime;

            // 更新渲染统计
            if (renderer && renderer.info) {
                this.stats.renderCalls = renderer.info.render.calls;
                this.stats.triangles = renderer.info.render.triangles;
                this.stats.points = renderer.info.render.points;
            }

            // 更新内存使用情况
            this.updateMemoryStats();

            // 检查性能阈值
            this.checkThresholds();

            // 通知回调函数
            this.notifyCallbacks();
        }
    }

    /**
     * 更新内存统计
     */
    updateMemoryStats() {
        if (performance.memory) {
            this.stats.memoryUsage = performance.memory.usedJSHeapSize;
        }
    }

    /**
     * 检查性能阈值
     */
    checkThresholds() {
        const warnings = [];

        // 检查FPS
        if (this.stats.fps < this.thresholds.lowFPS) {
            warnings.push({
                type: 'low_fps',
                message: `FPS过低: ${this.stats.fps}`,
                value: this.stats.fps,
                threshold: this.thresholds.lowFPS,
            });
        }

        // 检查内存使用
        if (this.stats.memoryUsage > this.thresholds.highMemory) {
            warnings.push({
                type: 'high_memory',
                message: `内存使用过高: ${(this.stats.memoryUsage / 1024 / 1024).toFixed(1)}MB`,
                value: this.stats.memoryUsage,
                threshold: this.thresholds.highMemory,
            });
        }

        // 检查点数
        if (this.stats.points > this.thresholds.maxPoints) {
            warnings.push({
                type: 'high_points',
                message: `渲染点数过多: ${this.stats.points}`,
                value: this.stats.points,
                threshold: this.thresholds.maxPoints,
            });
        }

        // 如果有警告，输出到控制台
        if (warnings.length > 0) {
            console.warn('性能警告:', warnings);
        }
    }

    /**
     * 添加性能回调
     */
    addCallback(callback) {
        this.callbacks.push(callback);
    }

    /**
     * 移除性能回调
     */
    removeCallback(callback) {
        const index = this.callbacks.indexOf(callback);
        if (index > -1) {
            this.callbacks.splice(index, 1);
        }
    }

    /**
     * 通知所有回调函数
     */
    notifyCallbacks() {
        this.callbacks.forEach(callback => {
            try {
                callback(this.stats);
            } catch (error) {
                console.error('性能回调执行错误:', error);
            }
        });
    }

    /**
     * 获取性能统计
     */
    getStats() {
        return {
            ...this.stats,
            avgFPS: this.getAverageFPS(),
            minFPS: Math.min(...this.fpsHistory),
            maxFPS: Math.max(...this.fpsHistory),
            memoryMB: Math.round(this.stats.memoryUsage / 1024 / 1024),
        };
    }

    /**
     * 获取平均FPS
     */
    getAverageFPS() {
        if (this.fpsHistory.length === 0) return 0;
        const sum = this.fpsHistory.reduce((a, b) => a + b, 0);
        return Math.round(sum / this.fpsHistory.length);
    }

    /**
     * 设置性能阈值
     */
    setThresholds(newThresholds) {
        this.thresholds = { ...this.thresholds, ...newThresholds };
        console.log('性能阈值已更新:', this.thresholds);
    }

    /**
     * 获取性能建议
     */
    getPerformanceAdvice() {
        const advice = [];
        const stats = this.getStats();

        if (stats.fps < 30) {
            advice.push({
                type: 'fps',
                severity: 'high',
                message: 'FPS过低，建议减少点云密度或启用LOD',
            });
        }

        if (stats.points > 100000) {
            advice.push({
                type: 'points',
                severity: 'medium',
                message: '渲染点数较多，建议使用LOD优化',
            });
        }

        if (stats.memoryMB > 200) {
            advice.push({
                type: 'memory',
                severity: 'medium',
                message: '内存使用较高，建议清理缓存',
            });
        }

        if (stats.renderCalls > 100) {
            advice.push({
                type: 'draw_calls',
                severity: 'low',
                message: '渲染调用次数较多，建议合并几何体',
            });
        }

        return advice;
    }

    /**
     * 重置统计数据
     */
    reset() {
        this.stats = {
            fps: 0,
            frameTime: 0,
            memoryUsage: 0,
            renderCalls: 0,
            triangles: 0,
            points: 0,
        };
        this.frameCount = 0;
        this.fpsHistory = [];
        this.lastTime = performance.now();
        console.log('性能统计已重置');
    }

    /**
     * 导出性能报告
     */
    exportReport() {
        const stats = this.getStats();
        const advice = this.getPerformanceAdvice();

        return {
            timestamp: new Date().toISOString(),
            stats: stats,
            advice: advice,
            thresholds: this.thresholds,
            fpsHistory: [...this.fpsHistory],
        };
    }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();
