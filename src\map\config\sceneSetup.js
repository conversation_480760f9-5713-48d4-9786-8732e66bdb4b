/**
 * 3D场景配置模块
 * 处理Three.js场景的初始化和配置
 */

import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { createNonReactiveThreeObject, makeThreeObjectNonReactive } from '../three/threeUtils.js';

/**
 * 场景配置类
 */
export class SceneSetup {
    /**
     * 初始化Three.js场景
     */
    static initThreeJS(container) {
        const sceneConfig = {};

        // 创建场景
        sceneConfig.scene = createNonReactiveThreeObject(THREE.Scene);
        sceneConfig.scene.background = new THREE.Color(0xf0f0f0);

        // 创建相机
        sceneConfig.camera = createNonReactiveThreeObject(THREE.PerspectiveCamera,
            75, // 视角
            container.clientWidth / container.clientHeight, // 宽高比
            0.1, // 近平面
            1000, // 远平面
        );
        // 设置俯视角度，Z轴向上
        sceneConfig.camera.position.set(0, -15, 10);
        sceneConfig.camera.lookAt(0, 0, 0);

        // 关键：设置相机的up向量为Z轴向上，这样左右拖拽就是水平旋转
        sceneConfig.camera.up.set(0, 0, 1);

        // 创建渲染器
        sceneConfig.renderer = createNonReactiveThreeObject(THREE.WebGLRenderer, { antialias: true });
        sceneConfig.renderer.setSize(container.clientWidth, container.clientHeight);
        sceneConfig.renderer.shadowMap.enabled = true;
        sceneConfig.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        container.appendChild(sceneConfig.renderer.domElement);

        // 添加控制器
        sceneConfig.controls = makeThreeObjectNonReactive(new OrbitControls(sceneConfig.camera, sceneConfig.renderer.domElement));
        sceneConfig.controls.enableDamping = true;
        sceneConfig.controls.dampingFactor = 0.05;

        // 配置正确的地图旋转行为
        sceneConfig.controls.screenSpacePanning = false;
        sceneConfig.controls.enableRotate = true;

        // 关键设置：确保左右拖拽是水平旋转，上下拖拽是垂直旋转
        sceneConfig.controls.enableDamping = true;
        sceneConfig.controls.dampingFactor = 0.05;

        // 设置旋转速度
        sceneConfig.controls.rotateSpeed = 1.0;

        // 限制垂直旋转角度，避免完全翻转
        sceneConfig.controls.minPolarAngle = 0.1; // 稍微限制最小角度，避免完全垂直向下
        sceneConfig.controls.maxPolarAngle = Math.PI - 0.1; // 稍微限制最大角度，避免完全垂直向上

        // 确保正确的坐标系映射
        sceneConfig.controls.target.set(0, 0, 0);

        return sceneConfig;
    }

    /**
     * 设置光源
     */
    static setupLighting(scene) {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        scene.add(ambientLight);

        // 方向光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        scene.add(directionalLight);

        return { ambientLight, directionalLight };
    }

    /**
     * 创建地板
     */
    static createFloor(scene) {
        const geometry = new THREE.PlaneGeometry(30, 30);
        const material = new THREE.MeshLambertMaterial({
            color: 0xcccccc,
            transparent: true,
            opacity: 0.95,
            polygonOffset: true, // 启用多边形偏移，避免与网格共面闪烁
            polygonOffsetFactor: 1,
            polygonOffsetUnits: 1,
        });
        const floor = new THREE.Mesh(geometry, material);
        // 地板在XY平面上，不需要旋转
        // floor.rotation.x = -Math.PI / 2  // 移除旋转
        floor.position.z = 0;  // 地板在Z=0平面
        floor.receiveShadow = true;
        floor.name = 'default-floor'; // 添加名称标识
        scene.add(floor);

        // 添加网格（在XY平面上，稍微抬高避免Z-fighting）
        const gridHelper = new THREE.GridHelper(30, 30, 0x888888, 0xaaaaaa);
        gridHelper.rotation.x = Math.PI / 2;  // 旋转90度使网格在XY平面
        gridHelper.position.z = 0.001;  // 稍微抬高避免Z-fighting
        gridHelper.material.opacity = 0.4;
        gridHelper.material.transparent = true;
        gridHelper.name = 'default-grid'; // 添加名称标识
        scene.add(gridHelper);

        // 添加坐标轴辅助器（可选，用于调试）
        const axesHelper = new THREE.AxesHelper(1);
        axesHelper.position.z = 0.04;  // 稍微抬高
        axesHelper.name = 'default-axes'; // 添加名称标识
        scene.add(axesHelper);
        // 红色=X轴(左右), 绿色=Y轴(前后), 蓝色=Z轴(上下)

        return { floor, gridHelper, axesHelper };
    }

    /**
     * 隐藏默认地板和网格
     */
    static hideDefaultFloor(scene) {
        const floor = scene.getObjectByName('default-floor');
        const grid = scene.getObjectByName('default-grid');

        if (floor) floor.visible = false;
        if (grid) grid.visible = false;
    }

    /**
     * 显示默认地板和网格
     */
    static showDefaultFloor(scene) {
        const floor = scene.getObjectByName('default-floor');
        const grid = scene.getObjectByName('default-grid');

        if (floor) floor.visible = true;
        if (grid) grid.visible = true;
    }

    /**
     * 移除默认地板和网格
     */
    static removeDefaultFloor(scene) {
        const floor = scene.getObjectByName('default-floor');
        const grid = scene.getObjectByName('default-grid');
        const axes = scene.getObjectByName('default-axes');

        if (floor) {
            scene.remove(floor);
            if (floor.geometry) floor.geometry.dispose();
            if (floor.material) floor.material.dispose();
        }
        if (grid) {
            scene.remove(grid);
            if (grid.geometry) grid.geometry.dispose();
            if (grid.material) grid.material.dispose();
        }
        if (axes) {
            scene.remove(axes);
            if (axes.geometry) axes.geometry.dispose();
            if (axes.material) axes.material.dispose();
        }
    }

    /**
     * 设置窗口大小调整事件
     */
    static setupWindowResize(camera, renderer, container) {
        const onWindowResize = () => {
            camera.aspect = container.clientWidth / container.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(container.clientWidth, container.clientHeight);
        };

        window.addEventListener('resize', onWindowResize);
        return onWindowResize;
    }

    /**
     * 设置拖放事件
     */
    static setupDragAndDrop(container, createObjectAtPosition) {
        container.addEventListener('dragover', e => {
            e.preventDefault();
        });

        container.addEventListener('drop', e => {
            e.preventDefault();
            const objectType = e.dataTransfer.getData('text/plain');
            createObjectAtPosition(objectType, e);
        });
    }

    /**
     * 设置点击事件处理
     */
    static setupClickHandler(renderer, camera, getSceneObjects, selectObject, container = null) {
        const onCanvasClick = event => {
            // 将鼠标点击的屏幕坐标转换为Three.js中的标准化设备坐标
            const rect = renderer.domElement.getBoundingClientRect();
            const mouse = new THREE.Vector2();
            mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
            mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

            // 射线投射器
            const raycaster = new THREE.Raycaster();
            raycaster.setFromCamera(mouse, camera);

            // 动态获取当前的场景对象
            const sceneObjects = getSceneObjects();

            // 性能优化：智能过滤检测对象，排除不需要点击的大型网格
            const allObjects = [];
            sceneObjects.forEach(obj => {
                if (obj.mesh) {
                    allObjects.push(obj.mesh);
                }
                // 如果是路径对象（Group类型），添加Group本身以便检测路径线条
                if (obj.type === 'Group') {
                    allObjects.push(obj);
                }
            });

            // 设置射线检测的性能优化参数
            raycaster.params.Points.threshold = 0.1; // 提高点云检测阈值
            raycaster.params.Line.threshold = 0.1;   // 提高线条检测阈值

            // 性能优化：分层检测，优先检测小对象（如控制点）
            let intersects = [];

            // 首先检测控制点和小对象
            const smallObjects = allObjects.filter(obj => {
                return obj.userData?.objectType === 'pathControlPoint' ||
                    obj.userData?.objectType === 'person' ||
                    (obj.geometry && obj.geometry.type === 'SphereGeometry');
            });

            if (smallObjects.length > 0) {
                intersects = raycaster.intersectObjects(smallObjects, true);
            }

            // 如果没有检测到小对象，再检测其他对象
            if (intersects.length === 0) {
                const otherObjects = allObjects.filter(obj => !smallObjects.includes(obj));
                intersects = raycaster.intersectObjects(otherObjects, true);
            }

            // 查找被点击的对象
            if (intersects.length > 0) {
                const clickedMesh = intersects[0].object;
                const clickedObject = SceneSetup.findClickedObject(clickedMesh, sceneObjects);

                if (clickedObject) {
                    selectObject(clickedObject);
                } else {
                    // 如果没找到常规对象，检查是否是路径相关的对象
                    if (clickedMesh.userData && (clickedMesh.userData.objectType === 'pathLine' || clickedMesh.userData.objectType === 'pathLineCollision')) {
                        // 找到对应的路径Group对象
                        const pathGroup = sceneObjects.find(obj =>
                            obj.type === 'Group' &&
                            obj.userData &&
                            obj.userData.objectUuid === clickedMesh.userData.pathUuid,
                        );
                        if (pathGroup) {
                            selectObject(pathGroup);
                            return;
                        }
                    } else if (clickedMesh.userData && clickedMesh.userData.objectType === 'pathControlPoint') {
                        // 找到对应的路径Group对象（通过控制点选中路径）
                        const pathGroup = sceneObjects.find(obj =>
                            obj.type === 'Group' &&
                            obj.userData &&
                            obj.userData.objectUuid === clickedMesh.userData.pathUuid,
                        );
                        if (pathGroup) {
                            selectObject(pathGroup);
                            return;
                        }
                    }
                    selectObject(null);
                }
            } else {
                selectObject(null);
            }
        };

        // 优先使用传入的container，如果没有则使用renderer.domElement
        const targetElement = container || renderer.domElement;
        targetElement.addEventListener('click', onCanvasClick);
        return onCanvasClick;
    }

    /**
     * 查找被点击的对象
     */
    static findClickedObject(clickedMesh, sceneObjects) {
        // 首先尝试直接匹配
        let clickedObject = sceneObjects.find(obj => obj.mesh === clickedMesh);

        // 如果没找到，可能是Group内部的子对象，向上查找
        if (!clickedObject) {
            let parent = clickedMesh.parent;
            while (parent && !clickedObject) {
                clickedObject = sceneObjects.find(obj => obj.mesh === parent);
                parent = parent.parent;
            }
        }

        // 还可以通过userData查找
        if (!clickedObject) {
            // 检查点击的mesh的userData
            if (clickedMesh.userData && clickedMesh.userData.objectType && clickedMesh.userData.objectId) {
                const { objectType, objectId } = clickedMesh.userData;
                clickedObject = sceneObjects.find(obj =>
                    obj.type === objectType && obj.id === objectId,
                );
            }

            // 检查父级的userData
            if (!clickedObject && clickedMesh.parent && clickedMesh.parent.userData) {
                const { objectType, objectId } = clickedMesh.parent.userData;
                if (objectType && objectId) {
                    clickedObject = sceneObjects.find(obj =>
                        obj.type === objectType && obj.id === objectId,
                    );
                }
            }
        }

        return clickedObject;
    }

    /**
     * 设置滚轮事件处理
     */
    static setupWheelHandler(renderer, handleWheelRotation) {
        const onCanvasWheel = event => {
            return handleWheelRotation(event);
        };

        // 监听鼠标滚轮事件用于调整yaw（使用捕获阶段，优先级更高）
        renderer.domElement.addEventListener('wheel', onCanvasWheel, {
            passive: false,
            capture: true, // 在捕获阶段处理，优先于OrbitControls
        });

        return onCanvasWheel;
    }

    /**
     * 清空场景（不显示确认对话框）
     */
    static clearScene(scene, sceneObjects) {
        // 移除所有对象
        sceneObjects.forEach(obj => {
            scene.remove(obj.mesh);
            if (obj.dispose) {
                obj.dispose();
            }
        });

        return [];
    }
}
